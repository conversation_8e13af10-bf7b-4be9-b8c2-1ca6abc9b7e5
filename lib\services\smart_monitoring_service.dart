import 'dart:async';
import 'package:flutter/foundation.dart';
import '../providers/smart_notification_provider.dart';
import '../providers/debt_provider.dart';
import '../providers/customer_provider.dart';
import '../providers/card_inventory_provider.dart';
import '../models/notification_model.dart';
import '../models/debt.dart';

class SmartMonitoringService {
  factory SmartMonitoringService() => _instance;
  SmartMonitoringService._internal();
  static final SmartMonitoringService _instance =
      SmartMonitoringService._internal();

  Timer? _monitoringTimer;
  bool _isMonitoring = false;

  // مزودي البيانات
  SmartNotificationProvider? _notificationProvider;
  DebtProvider? _debtProvider;
  CustomerProvider? _customerProvider;
  CardInventoryProvider? _inventoryProvider;

  // إعدادات المراقبة
  static const Duration _monitoringInterval = Duration(
    minutes: 5,
  ); // كل 5 دقائق
  static const Duration _quickCheckInterval = Duration(
    minutes: 1,
  ); // فحص سريع كل دقيقة

  // تهيئة الخدمة
  void initialize({
    required SmartNotificationProvider notificationProvider,
    required DebtProvider debtProvider,
    required CustomerProvider customerProvider,
    required CardInventoryProvider inventoryProvider,
  }) {
    _notificationProvider = notificationProvider;
    _debtProvider = debtProvider;
    _customerProvider = customerProvider;
    _inventoryProvider = inventoryProvider;

    debugPrint('🤖 Smart Monitoring Service initialized');
  }

  // بدء المراقبة التلقائية
  void startMonitoring() {
    if (_isMonitoring) return;

    _isMonitoring = true;
    debugPrint('🚀 Starting Smart Monitoring Service...');

    // فحص فوري عند البدء
    _performSmartCheck();

    // بدء المراقبة الدورية
    _monitoringTimer = Timer.periodic(_monitoringInterval, (timer) {
      _performSmartCheck();
    });

    // فحص سريع للحالات العاجلة
    Timer.periodic(_quickCheckInterval, (timer) {
      _performQuickCheck();
    });

    debugPrint('✅ Smart Monitoring Service started successfully');
  }

  // إيقاف المراقبة
  void stopMonitoring() {
    if (!_isMonitoring) return;

    _monitoringTimer?.cancel();
    _isMonitoring = false;
    debugPrint('🛑 Smart Monitoring Service stopped');
  }

  // الفحص الذكي الشامل
  Future<void> _performSmartCheck() async {
    if (!_isMonitoring || _notificationProvider == null) return;

    try {
      debugPrint('🔍 Performing smart check...');

      // تحديث التنبيهات الذكية
      await _notificationProvider!.generateSmartNotifications(
        debtProvider: _debtProvider!,
        customerProvider: _customerProvider!,
        inventoryProvider: _inventoryProvider!,
      );

      // فحص الحالات الحرجة
      await _checkCriticalConditions();

      // تنظيف التنبيهات القديمة
      _cleanupOldNotifications();

      debugPrint('✅ Smart check completed');
    } catch (e) {
      debugPrint('❌ Error in smart check: $e');
    }
  }

  // الفحص السريع للحالات العاجلة
  Future<void> _performQuickCheck() async {
    if (!_isMonitoring || _notificationProvider == null) return;

    try {
      // فحص الديون المتأخرة الجديدة
      await _checkNewOverdueDebts();

      // فحص نفاد المخزون الطارئ
      await _checkEmergencyStockOut();
    } catch (e) {
      debugPrint('❌ Error in quick check: $e');
    }
  }

  // فحص الحالات الحرجة
  Future<void> _checkCriticalConditions() async {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    // عدد الديون المتأخرة
    int overdueCount = 0;
    double overdueAmount = 0;

    // عدد الديون المستحقة اليوم
    int dueTodayCount = 0;
    double dueTodayAmount = 0;

    // فحص الديون - عرض جميع الديون بما في ذلك المدفوعة
    for (final debt in _debtProvider!.debts) {
      // if (debt.status == DebtStatus.paid) continue;

      final dueDate = DateTime(
        debt.dueDate.year,
        debt.dueDate.month,
        debt.dueDate.day,
      );

      final daysDifference = dueDate.difference(today).inDays;

      if (daysDifference < 0) {
        overdueCount++;
        overdueAmount += debt.remainingAmount;
      } else if (daysDifference == 0) {
        dueTodayCount++;
        dueTodayAmount += debt.remainingAmount;
      }
    }

    // إنشاء تنبيهات للحالات الحرجة
    if (overdueCount >= 5) {
      _createCriticalNotification(
        'تحذير: ديون متأخرة كثيرة',
        'لديك $overdueCount ديون متأخرة بقيمة ${_formatCurrency(overdueAmount)}',
        NotificationType.overdue,
        NotificationPriority.urgent,
      );
    }

    if (dueTodayCount >= 3) {
      _createCriticalNotification(
        'تنبيه: ديون مستحقة اليوم',
        'لديك $dueTodayCount ديون مستحقة اليوم بقيمة ${_formatCurrency(dueTodayAmount)}',
        NotificationType.dueToday,
        NotificationPriority.high,
      );
    }

    // فحص المخزون الحرج
    int lowStockCount = 0;
    int outOfStockCount = 0;

    for (final inventory in _inventoryProvider!.inventories) {
      if (inventory.isOutOfStock) {
        outOfStockCount++;
      } else if (inventory.isLowStock) {
        lowStockCount++;
      }
    }

    if (outOfStockCount >= 3) {
      _createCriticalNotification(
        'تحذير: نفاد مخزون متعدد',
        'نفد مخزون $outOfStockCount أنواع من البطاقات',
        NotificationType.outOfStock,
        NotificationPriority.urgent,
      );
    }

    if (lowStockCount >= 5) {
      _createCriticalNotification(
        'تنبيه: مخزون منخفض متعدد',
        'مخزون $lowStockCount أنواع من البطاقات منخفض',
        NotificationType.lowStock,
        NotificationPriority.high,
      );
    }
  }

  // فحص الديون المتأخرة الجديدة
  Future<void> _checkNewOverdueDebts() async {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    for (final debt in _debtProvider!.debts) {
      // if (debt.status == DebtStatus.paid) continue;

      final dueDate = DateTime(
        debt.dueDate.year,
        debt.dueDate.month,
        debt.dueDate.day,
      );

      // إذا أصبح الدين متأخراً اليوم
      if (dueDate.isAtSameMomentAs(today.subtract(const Duration(days: 1)))) {
        final customer = _customerProvider!.customers.firstWhere(
          (c) => c.id == debt.customerId,
        );

        _createCriticalNotification(
          'دين جديد متأخر!',
          'دين ${customer.name} أصبح متأخراً اليوم - ${_formatCurrency(debt.remainingAmount)}',
          NotificationType.overdue,
          NotificationPriority.urgent,
        );
      }
    }
  }

  // فحص نفاد المخزون الطارئ
  Future<void> _checkEmergencyStockOut() async {
    for (final inventory in _inventoryProvider!.inventories) {
      // إذا نفد المخزون حديثاً (الكمية = 0 والحد الأدنى > 0)
      if (inventory.quantity == 0 && inventory.minQuantity > 0) {
        // التحقق من عدم وجود تنبيه مماثل حديث
        final existingNotification = _notificationProvider!.notifications
            .where(
              (n) =>
                  n.type == NotificationType.outOfStock &&
                  n.additionalData?['cardType'] == inventory.cardType &&
                  DateTime.now().difference(n.createdAt).inHours < 1,
            )
            .isNotEmpty;

        if (!existingNotification) {
          _createCriticalNotification(
            'نفاد مخزون طارئ!',
            'نفد مخزون ${inventory.cardType} تماماً',
            NotificationType.outOfStock,
            NotificationPriority.urgent,
          );
        }
      }
    }
  }

  // إنشاء تنبيه حرج
  void _createCriticalNotification(
    String title,
    String message,
    NotificationType type,
    NotificationPriority priority,
  ) {
    // التحقق من عدم وجود تنبيه مماثل حديث
    final existingNotification = _notificationProvider!.notifications
        .where(
          (n) =>
              n.title == title &&
              DateTime.now().difference(n.createdAt).inMinutes < 30,
        )
        .isNotEmpty;

    if (!existingNotification) {
      final notification = NotificationModel(
        id: 'critical_${DateTime.now().millisecondsSinceEpoch}',
        title: title,
        message: message,
        type: type,
        priority: priority,
        createdAt: DateTime.now(),
      );

      _notificationProvider!.addExternalNotification(notification);
      debugPrint('🚨 Critical notification created: $title');
    }
  }

  // تنظيف التنبيهات القديمة
  void _cleanupOldNotifications() {
    final cutoffDate = DateTime.now().subtract(const Duration(days: 7));

    final oldNotifications = _notificationProvider!.notifications
        .where(
          (notification) =>
              notification.createdAt.isBefore(cutoffDate) &&
              notification.isRead,
        )
        .toList();

    for (final notification in oldNotifications) {
      _notificationProvider!.removeNotification(notification.id);
    }

    debugPrint('🧹 Cleaned up ${oldNotifications.length} old notifications');
  }

  // تنسيق العملة
  String _formatCurrency(double amount) {
    return '${amount.toStringAsFixed(0)} د ع';
  }

  // الحصول على حالة المراقبة
  bool get isMonitoring => _isMonitoring;

  // الحصول على إحصائيات المراقبة
  Map<String, dynamic> getMonitoringStats() {
    if (_notificationProvider == null) return {};

    final stats = _notificationProvider!.getNotificationStats();
    return {
      ...stats,
      'isMonitoring': _isMonitoring,
      'lastCheck': DateTime.now().toIso8601String(),
      'monitoringInterval': _monitoringInterval.inMinutes,
    };
  }
}

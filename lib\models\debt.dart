enum DebtStatus { pending, paid, partiallyPaid }

enum DebtDirection {
  customerOwesMe, // العميل يدين لي (الوضع الافتراضي)
  iOweCustomer, // أنا أدين للعميل (يطلبني)
}

class Debt {
  Debt({
    this.id,
    required this.customerId,
    required this.itemName,
    required this.quantity,
    required this.amount,
    this.paidAmount = 0.0,
    required this.cardType,
    this.notes,
    required this.entryDate,
    required this.dueDate,
    required this.createdAt,
    required this.updatedAt,
    this.status = DebtStatus.pending,
    this.direction = DebtDirection.customerOwesMe, // الاتجاه الافتراضي
    this.firstPaymentDate, // تاريخ أول تسديد - يحفظ مرة واحدة فقط
    this.isArchived = false, // حقل الأرشفة - افتراضي غير مؤرشف
  });

  factory Debt.fromMap(Map<String, dynamic> map) {
    return Debt(
      id: map['id']?.toInt(),
      customerId: map['customer_id']?.toInt() ?? 0,
      itemName: map['item_name'] ?? '',
      quantity: map['quantity']?.toInt() ?? 0,
      amount: map['amount']?.toDouble() ?? 0.0,
      paidAmount: map['paid_amount']?.toDouble() ?? 0.0,
      cardType: _parseCardType(map['card_type']), // Handle both int and String
      notes: map['notes'],
      entryDate: DateTime.fromMillisecondsSinceEpoch(map['entry_date']),
      dueDate: DateTime.fromMillisecondsSinceEpoch(map['due_date']),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at']),
      status: _parseDebtStatus(map['status']),
      direction: _parseDebtDirection(map['direction']),
      firstPaymentDate: map['first_payment_date'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['first_payment_date'])
          : null,
      isArchived: map['is_archived'] == 1, // تحويل من int إلى bool
    );
  }

  // Helper method to parse card type from both int and String
  static String _parseCardType(dynamic cardType) {
    if (cardType == null) return 'cash';

    if (cardType is String) {
      return cardType;
    }

    if (cardType is int) {
      // Convert old int values to new string values
      switch (cardType) {
        case 0:
          return 'cash';
        case 1:
          return 'visa';
        case 2:
          return 'mastercard';
        case 3:
          return 'mada';
        default:
          return 'cash';
      }
    }

    return 'cash'; // Default fallback
  }

  // Helper method to parse debt status safely
  static DebtStatus _parseDebtStatus(dynamic status) {
    if (status == null) return DebtStatus.pending;

    if (status is int) {
      if (status >= 0 && status < DebtStatus.values.length) {
        return DebtStatus.values[status];
      }
    }

    return DebtStatus.pending; // Default fallback
  }

  // Helper method to parse debt direction safely
  static DebtDirection _parseDebtDirection(dynamic direction) {
    if (direction == null) return DebtDirection.customerOwesMe;

    if (direction is int) {
      if (direction >= 0 && direction < DebtDirection.values.length) {
        return DebtDirection.values[direction];
      }
    }

    return DebtDirection.customerOwesMe; // Default fallback
  }

  final int? id;
  final int customerId;
  final String itemName;
  final int quantity;
  final double amount;
  final double paidAmount;
  final String cardType; // Changed from CardType to String
  final String? notes;
  final DateTime entryDate;
  final DateTime dueDate;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DebtStatus status;
  final DebtDirection direction; // اتجاه الدين
  final DateTime? firstPaymentDate; // تاريخ أول تسديد - يحفظ مرة واحدة فقط
  final bool isArchived; // حقل الأرشفة

  double get remainingAmount => amount - paidAmount;
  bool get isPaid => status == DebtStatus.paid;
  bool get isPartiallyPaid => status == DebtStatus.partiallyPaid;
  bool get isPending => status == DebtStatus.pending;
  bool get isOverdue => DateTime.now().isAfter(dueDate) && !isPaid;

  // خصائص اتجاه الدين
  bool get isCustomerOwesMe => direction == DebtDirection.customerOwesMe;
  bool get isIOweCustomer => direction == DebtDirection.iOweCustomer;

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'customer_id': customerId,
      'item_name': itemName,
      'quantity': quantity,
      'amount': amount,
      'paid_amount': paidAmount,
      'card_type': cardType, // Changed to use String directly
      'notes': notes,
      'entry_date': entryDate.millisecondsSinceEpoch,
      'due_date': dueDate.millisecondsSinceEpoch,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
      'status': status.index,
      'direction': direction.index,
      'first_payment_date': firstPaymentDate?.millisecondsSinceEpoch,
      'is_archived': isArchived ? 1 : 0, // تحويل من bool إلى int
    };
  }

  Debt copyWith({
    int? id,
    int? customerId,
    String? itemName,
    int? quantity,
    double? amount,
    double? paidAmount,
    String? cardType, // Changed from CardType to String
    String? notes,
    DateTime? entryDate,
    DateTime? dueDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    DebtStatus? status,
    DebtDirection? direction,
    DateTime? firstPaymentDate,
    bool? isArchived,
  }) {
    return Debt(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      itemName: itemName ?? this.itemName,
      quantity: quantity ?? this.quantity,
      amount: amount ?? this.amount,
      paidAmount: paidAmount ?? this.paidAmount,
      cardType: cardType ?? this.cardType,
      notes: notes ?? this.notes,
      entryDate: entryDate ?? this.entryDate,
      dueDate: dueDate ?? this.dueDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      status: status ?? this.status,
      direction: direction ?? this.direction,
      firstPaymentDate: firstPaymentDate ?? this.firstPaymentDate,
      isArchived: isArchived ?? this.isArchived,
    );
  }

  @override
  String toString() {
    return 'Debt(id: $id, customerId: $customerId, itemName: $itemName, quantity: $quantity, amount: $amount, paidAmount: $paidAmount, cardType: $cardType, notes: $notes, entryDate: $entryDate, dueDate: $dueDate, createdAt: $createdAt, updatedAt: $updatedAt, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Debt &&
        other.id == id &&
        other.customerId == customerId &&
        other.itemName == itemName &&
        other.quantity == quantity &&
        other.amount == amount &&
        other.paidAmount == paidAmount &&
        other.cardType == cardType &&
        other.notes == notes &&
        other.entryDate == entryDate &&
        other.dueDate == dueDate &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt &&
        other.status == status;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        customerId.hashCode ^
        itemName.hashCode ^
        quantity.hashCode ^
        amount.hashCode ^
        paidAmount.hashCode ^
        cardType.hashCode ^
        notes.hashCode ^
        entryDate.hashCode ^
        dueDate.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode ^
        status.hashCode;
  }
}

extension DebtStatusExtension on DebtStatus {
  String get displayName {
    switch (this) {
      case DebtStatus.pending:
        return 'غير مدفوع';
      case DebtStatus.paid:
        return 'مدفوع';
      case DebtStatus.partiallyPaid:
        return 'مدفوع جزئياً';
    }
  }
}

extension DebtDirectionExtension on DebtDirection {
  String get displayName {
    switch (this) {
      case DebtDirection.customerOwesMe:
        return 'العميل يدين لي';
      case DebtDirection.iOweCustomer:
        return 'أنا أدين للعميل';
    }
  }

  String get shortDisplayName {
    switch (this) {
      case DebtDirection.customerOwesMe:
        return 'عليه';
      case DebtDirection.iOweCustomer:
        return 'له';
    }
  }
}

import 'package:flutter/foundation.dart';
import '../models/card_inventory.dart';
import '../database/database_helper.dart';

class CardInventoryProvider with ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  List<CardInventory> _inventories = [];
  bool _isLoading = false;

  List<CardInventory> get inventories => _inventories;
  bool get isLoading => _isLoading;

  // تحميل جميع الكميات
  Future<void> loadInventories() async {
    _isLoading = true;
    notifyListeners();

    try {
      _inventories = await _databaseHelper.getAllCardInventories();
    } catch (e) {
      debugPrint('Error loading inventories: $e');
      _inventories = [];
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // إضافة كمية جديدة
  Future<void> addInventory(CardInventory inventory) async {
    try {
      await _databaseHelper.insertCardInventory(inventory);
      await loadInventories();
    } catch (e) {
      debugPrint('Error adding inventory: $e');
      rethrow;
    }
  }

  // تحديث كمية
  Future<void> updateInventory(CardInventory inventory) async {
    try {
      await _databaseHelper.updateCardInventory(inventory);
      await loadInventories();
    } catch (e) {
      debugPrint('Error updating inventory: $e');
      rethrow;
    }
  }

  // حذف كمية
  Future<void> deleteInventory(int inventoryId) async {
    try {
      await _databaseHelper.deleteCardInventory(inventoryId);
      await loadInventories();
    } catch (e) {
      debugPrint('Error deleting inventory: $e');
      rethrow;
    }
  }

  // إضافة كمية لنوع بطاقة معين
  Future<bool> addStock(String cardType, int quantity) async {
    try {
      final existingInventory = _inventories.firstWhere(
        (inv) => inv.cardType == cardType,
        orElse: () => CardInventory(cardType: cardType, quantity: 0),
      );

      if (existingInventory.id != null) {
        // تحديث الكمية الموجودة
        final updatedInventory = existingInventory.copyWith(
          quantity: existingInventory.quantity + quantity,
          updatedAt: DateTime.now(),
        );
        await updateInventory(updatedInventory);
      } else {
        // إضافة نوع بطاقة جديد
        final newInventory = CardInventory(
          cardType: cardType,
          quantity: quantity,
        );
        await addInventory(newInventory);
      }
      return true;
    } catch (e) {
      debugPrint('Error adding stock: $e');
      return false;
    }
  }

  // استقطاع كمية (عند البيع)
  Future<bool> deductStock(String cardType, int quantity) async {
    try {
      final existingInventory = _inventories.firstWhere(
        (inv) => inv.cardType == cardType,
        orElse: () => CardInventory(cardType: cardType, quantity: 0),
      );

      if (existingInventory.id == null) {
        // لا يوجد مخزون لهذا النوع
        debugPrint('⚠️ لا يوجد مخزون لنوع البطاقة: $cardType');
        return false;
      }

      if (existingInventory.quantity < quantity) {
        // الكمية المطلوبة أكبر من المتوفر
        debugPrint(
          '⚠️ الكمية المطلوبة ($quantity) أكبر من المتوفر (${existingInventory.quantity})',
        );
        return false;
      }

      // استقطاع الكمية
      final updatedInventory = existingInventory.copyWith(
        quantity: existingInventory.quantity - quantity,
        updatedAt: DateTime.now(),
      );

      await updateInventory(updatedInventory);
      debugPrint(
        '✅ تم استقطاع $quantity من $cardType. المتبقي: ${updatedInventory.quantity}',
      );
      return true;
    } catch (e) {
      debugPrint('Error deducting stock: $e');
      return false;
    }
  }

  // الحصول على كمية نوع بطاقة معين
  int getStockQuantity(String cardType) {
    try {
      final inventory = _inventories.firstWhere(
        (inv) => inv.cardType == cardType,
      );
      return inventory.quantity;
    } catch (e) {
      return 0;
    }
  }

  // الحصول على البطاقات منخفضة المخزون
  List<CardInventory> getLowStockItems() {
    return _inventories.where((inv) => inv.isLowStock).toList();
  }

  // الحصول على البطاقات النافدة
  List<CardInventory> getOutOfStockItems() {
    return _inventories.where((inv) => inv.isOutOfStock).toList();
  }

  // إجمالي قيمة المخزون (تقديري)
  double getTotalInventoryValue() {
    final Map<String, double> estimatedPrices = {
      'زين': 6500.0,
      'آسيا': 7500.0,
      'أبو العشرة': 9800.0,
      'أبو الستة': 4800.0,
      'نقدي': 0.0,
    };

    double total = 0.0;
    for (final inventory in _inventories) {
      final price = estimatedPrices[inventory.cardType] ?? 5000.0;
      total += inventory.quantity * price;
    }
    return total;
  }

  // إجمالي عدد البطاقات
  int getTotalCardsCount() {
    int total = 0;
    for (final inventory in _inventories) {
      total += inventory.quantity;
    }
    return total;
  }
}

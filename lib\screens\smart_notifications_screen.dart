import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/smart_notification_provider.dart';
import '../providers/debt_provider.dart';
import '../providers/customer_provider.dart';
import '../providers/card_inventory_provider.dart';
import '../providers/card_type_provider.dart';
import '../providers/notification_provider.dart';
import '../models/notification_model.dart';
import '../models/custom_card_type.dart';
import '../utils/number_formatter.dart';
import 'package:intl/intl.dart';

class SmartNotificationsScreen extends StatefulWidget {
  const SmartNotificationsScreen({super.key});

  @override
  State<SmartNotificationsScreen> createState() =>
      _SmartNotificationsScreenState();
}

class _SmartNotificationsScreenState extends State<SmartNotificationsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  NotificationType? _selectedFilter;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadNotifications();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadNotifications() async {
    try {
      debugPrint('🔄 بدء تحديث الإشعارات في صفحة الإشعارات الذكية...');

      final smartNotificationProvider = Provider.of<SmartNotificationProvider>(
        context,
        listen: false,
      );
      final debtProvider = Provider.of<DebtProvider>(context, listen: false);
      final customerProvider = Provider.of<CustomerProvider>(
        context,
        listen: false,
      );
      final inventoryProvider = Provider.of<CardInventoryProvider>(
        context,
        listen: false,
      );
      final notificationProvider = Provider.of<NotificationProvider>(
        context,
        listen: false,
      );

      // تحميل البيانات أولاً إذا لم تكن محملة
      if (debtProvider.debts.isEmpty) {
        debugPrint('📊 تحميل الديون...');
        await debtProvider.loadAllDebts();
      }

      if (customerProvider.customers.isEmpty) {
        debugPrint('👥 تحميل العملاء...');
        await customerProvider.loadCustomers();
      }

      if (inventoryProvider.inventories.isEmpty) {
        debugPrint('📦 تحميل المخزون...');
        await inventoryProvider.loadInventories();
      }

      debugPrint('📊 البيانات المتاحة:');
      debugPrint('   - الديون: ${debtProvider.debts.length}');
      debugPrint('   - العملاء: ${customerProvider.customers.length}');
      debugPrint('   - المخزون: ${inventoryProvider.inventories.length}');

      // إنشاء الإشعارات
      await smartNotificationProvider.generateSmartNotifications(
        debtProvider: debtProvider,
        customerProvider: customerProvider,
        inventoryProvider: inventoryProvider,
        notificationProvider: notificationProvider,
      );

      debugPrint(
        '✅ تم تحديث الإشعارات: ${smartNotificationProvider.notifications.length}',
      );

      // إضافة إشعارات تجريبية إذا لم توجد إشعارات
      if (smartNotificationProvider.notifications.isEmpty) {
        debugPrint('⚠️ لا توجد إشعارات، سيتم إضافة إشعارات تجريبية');

        // إنشاء إشعارات من البيانات الحقيقية بدلاً من الأسماء الافتراضية
        await _createRealNotificationsFromData();

        debugPrint('✅ تم تحديث الإشعارات من البيانات الحقيقية');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحديث الإشعارات: $e');
    }
  }

  // إنشاء إشعارات من البيانات الحقيقية
  Future<void> _createRealNotificationsFromData() async {
    try {
      final customerProvider = Provider.of<CustomerProvider>(
        context,
        listen: false,
      );
      final debtProvider = Provider.of<DebtProvider>(context, listen: false);
      final smartNotificationProvider = Provider.of<SmartNotificationProvider>(
        context,
        listen: false,
      );

      final customers = customerProvider.customers;
      final debts = debtProvider.debts;

      if (customers.isNotEmpty && debts.isNotEmpty) {
        int notificationsAdded = 0;

        // البحث عن ديون متأخرة - عرض جميع العملاء
        for (final customer in customers) {
          // جميع العملاء بدون حد أقصى
          final customerDebts =
              debts.where((debt) => debt.customerId == customer.id).toList();

          if (customerDebts.isNotEmpty) {
            final overdueDebts = customerDebts.where((debt) {
              return debt.dueDate.isBefore(DateTime.now()) &&
                  debt.remainingAmount > 0;
            }).toList();

            if (overdueDebts.isNotEmpty) {
              final totalAmount = overdueDebts.fold<double>(
                0,
                (sum, debt) => sum + debt.remainingAmount,
              );

              final overdueNotification = NotificationModel(
                id: 'real_overdue_${customer.id}_${DateTime.now().millisecondsSinceEpoch}',
                title: 'العميل ${customer.name} لديه دين متأخر',
                message:
                    'دين ${customer.name} متأخر ${DateTime.now().difference(overdueDebts.first.dueDate).inDays} أيام - ${_formatAmount(totalAmount)}',
                type: NotificationType.overdue,
                priority: NotificationPriority.urgent,
                createdAt: DateTime.now(),
                customerId: customer.id?.toString(),
                customerName: customer.name,
                amount: totalAmount,
                dueDate: overdueDebts.first.dueDate,
                additionalData: {
                  'customerName': customer.name,
                  'phoneNumber': customer.phone ?? '',
                  'totalAmount': totalAmount,
                  'debtCount': overdueDebts.length,
                  'maxDaysOverdue': DateTime.now()
                      .difference(overdueDebts.first.dueDate)
                      .inDays,
                },
              );

              smartNotificationProvider.addExternalNotification(
                overdueNotification,
              );
              notificationsAdded++;
            }

            // البحث عن ديون مستحقة اليوم
            final dueTodayDebts = customerDebts.where((debt) {
              final today = DateTime.now();
              final dueDate = debt.dueDate;
              return dueDate.year == today.year &&
                  dueDate.month == today.month &&
                  dueDate.day == today.day &&
                  debt.remainingAmount > 0;
            }).toList();

            if (dueTodayDebts.isNotEmpty && notificationsAdded < 3) {
              final totalAmount = dueTodayDebts.fold<double>(
                0,
                (sum, debt) => sum + debt.remainingAmount,
              );

              final dueTodayNotification = NotificationModel(
                id: 'real_due_today_${customer.id}_${DateTime.now().millisecondsSinceEpoch}',
                title: 'العميل ${customer.name} لديه دين مستحق اليوم',
                message:
                    'دين ${customer.name} مستحق اليوم - ${_formatAmount(totalAmount)}',
                type: NotificationType.dueToday,
                priority: NotificationPriority.high,
                createdAt: DateTime.now(),
                customerId: customer.id?.toString(),
                customerName: customer.name,
                amount: totalAmount,
                dueDate: dueTodayDebts.first.dueDate,
                additionalData: {
                  'customerName': customer.name,
                  'phoneNumber': customer.phone ?? '',
                  'totalAmount': totalAmount,
                  'debtCount': dueTodayDebts.length,
                },
              );

              smartNotificationProvider.addExternalNotification(
                dueTodayNotification,
              );
              notificationsAdded++;
            }

            // إزالة الحد الأقصى - عرض جميع التنبيهات
          }
        }

        if (notificationsAdded == 0) {
          // إذا لم توجد ديون حقيقية، أضف إشعار عام
          final generalNotification = NotificationModel(
            id: 'general_${DateTime.now().millisecondsSinceEpoch}',
            title: 'نظام الإشعارات الذكية',
            message: 'لا توجد ديون متأخرة أو مستحقة حالياً',
            type: NotificationType.dueToday,
            priority: NotificationPriority.low,
            createdAt: DateTime.now(),
          );
          smartNotificationProvider.addExternalNotification(
            generalNotification,
          );
        }

        debugPrint('✅ تم إضافة $notificationsAdded إشعار من البيانات الحقيقية');
      }
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء إشعارات من البيانات الحقيقية: $e');
    }
  }

  // تنسيق المبلغ
  String _formatAmount(double amount) {
    if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)} ألف';
    } else {
      return '${amount.toStringAsFixed(0)} ر.س';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          'التنبيهات الذكية',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          Consumer<SmartNotificationProvider>(
            builder: (context, provider, _) {
              if (provider.unreadCount > 0) {
                return Stack(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.mark_email_read),
                      onPressed: () => provider.markAllAsRead(),
                      tooltip: 'تحديد الكل كمقروء',
                    ),
                    Positioned(
                      right: 8,
                      top: 8,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 16,
                          minHeight: 16,
                        ),
                        child: Text(
                          '${provider.unreadCount}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ],
                );
              }
              return const SizedBox.shrink();
            },
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadNotifications,
            tooltip: 'تحديث',
          ),
        ],
        bottom: _buildStatsTabBar(),
      ),
      body: Column(
        children: [
          _buildFilterChips(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAllNotifications(),
                _buildDebtNotifications(),
                _buildInventoryNotifications(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _loadNotifications,
        backgroundColor: Colors.blue.shade600,
        child: const Icon(Icons.refresh, color: Colors.white),
      ),
    );
  }

  PreferredSizeWidget _buildStatsTabBar() {
    return PreferredSize(
      preferredSize: const Size.fromHeight(kToolbarHeight),
      child: Consumer<SmartNotificationProvider>(
        builder: (context, provider, _) {
          final stats = provider.getNotificationStats();
          return TabBar(
            controller: _tabController,
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white70,
            indicatorColor: Colors.white,
            tabs: [
              Tab(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.notifications, size: 18),
                        const SizedBox(width: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: stats['total']! > 0
                                ? Colors.white.withValues(alpha: 0.3)
                                : Colors.white.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            '${stats['total']}',
                            style: const TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 2),
                    const Text('الكل', style: TextStyle(fontSize: 12)),
                  ],
                ),
              ),
              Tab(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.account_balance_wallet, size: 18),
                        const SizedBox(width: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: (stats['overdue']! +
                                        stats['dueToday']! +
                                        stats['dueSoon']!) >
                                    0
                                ? Colors.red.withValues(alpha: 0.8)
                                : Colors.white.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            '${stats['overdue']! + stats['dueToday']! + stats['dueSoon']!}',
                            style: const TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 2),
                    const Text('الديون', style: TextStyle(fontSize: 12)),
                  ],
                ),
              ),
              Tab(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.inventory, size: 18),
                        const SizedBox(width: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color:
                                (stats['lowStock']! + stats['outOfStock']!) > 0
                                    ? Colors.orange.withValues(alpha: 0.8)
                                    : Colors.white.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            '${stats['lowStock']! + stats['outOfStock']!}',
                            style: const TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 2),
                    const Text('المخزون', style: TextStyle(fontSize: 12)),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildFilterChips() {
    final filters = [
      {'type': null, 'label': 'الكل', 'icon': Icons.all_inclusive},
      {
        'type': NotificationType.overdue,
        'label': 'متأخر',
        'icon': Icons.warning,
      },
      {
        'type': NotificationType.dueToday,
        'label': 'اليوم',
        'icon': Icons.today,
      },
      {
        'type': NotificationType.dueSoon,
        'label': 'قريباً',
        'icon': Icons.schedule,
      },
      {
        'type': NotificationType.lowStock,
        'label': 'مخزون منخفض',
        'icon': Icons.inventory_2,
      },
      {
        'type': NotificationType.outOfStock,
        'label': 'نافد',
        'icon': Icons.remove_shopping_cart,
      },
    ];

    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: filters.length,
        itemBuilder: (context, index) {
          final filter = filters[index];
          final isSelected = _selectedFilter == filter['type'];

          return Container(
            margin: const EdgeInsets.only(right: 8),
            child: FilterChip(
              selected: isSelected,
              label: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    filter['icon'] as IconData,
                    size: 16,
                    color: isSelected ? Colors.white : Colors.blue.shade600,
                  ),
                  const SizedBox(width: 4),
                  Text(filter['label'] as String),
                ],
              ),
              onSelected: (selected) {
                setState(() {
                  _selectedFilter =
                      selected ? filter['type'] as NotificationType? : null;
                });
              },
              selectedColor: Colors.blue.shade600,
              labelStyle: TextStyle(
                color: isSelected ? Colors.white : Colors.blue.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildAllNotifications() {
    return Consumer<SmartNotificationProvider>(
      builder: (context, provider, _) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        var notifications = provider.notifications;

        if (_selectedFilter != null) {
          notifications = provider.getNotificationsByType(_selectedFilter!);
        }

        if (notifications.isEmpty) {
          return _buildEmptyState();
        }

        return RefreshIndicator(
          onRefresh: _loadNotifications,
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: notifications.length,
            itemBuilder: (context, index) {
              return _buildNotificationCard(notifications[index], provider);
            },
          ),
        );
      },
    );
  }

  Widget _buildDebtNotifications() {
    return Consumer<SmartNotificationProvider>(
      builder: (context, provider, _) {
        final debtNotifications = provider.notifications
            .where(
              (n) =>
                  n.type == NotificationType.overdue ||
                  n.type == NotificationType.dueToday ||
                  n.type == NotificationType.dueSoon,
            )
            .toList();

        if (debtNotifications.isEmpty) {
          return _buildEmptyState(message: 'لا توجد تنبيهات ديون');
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: debtNotifications.length,
          itemBuilder: (context, index) {
            return _buildNotificationCard(debtNotifications[index], provider);
          },
        );
      },
    );
  }

  Widget _buildInventoryNotifications() {
    return Consumer<SmartNotificationProvider>(
      builder: (context, provider, _) {
        final inventoryNotifications = provider.notifications
            .where(
              (n) =>
                  n.type == NotificationType.lowStock ||
                  n.type == NotificationType.outOfStock,
            )
            .toList();

        if (inventoryNotifications.isEmpty) {
          return _buildEmptyState(message: 'لا توجد تنبيهات مخزون');
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: inventoryNotifications.length,
          itemBuilder: (context, index) {
            return _buildNotificationCard(
              inventoryNotifications[index],
              provider,
            );
          },
        );
      },
    );
  }

  Widget _buildNotificationCard(
    NotificationModel notification,
    SmartNotificationProvider provider,
  ) {
    final isDebtNotification = notification.type == NotificationType.overdue ||
        notification.type == NotificationType.dueToday ||
        notification.type == NotificationType.dueSoon;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: notification.isRead
              ? Colors.grey.shade300
              : _getPriorityColor(notification.priority),
          width: notification.isRead ? 1 : 2,
        ),
        boxShadow: [
          BoxShadow(
            color: _getPriorityColor(
              notification.priority,
            ).withValues(alpha: 0.1),
            blurRadius: notification.isRead ? 4 : 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: () {
          if (!notification.isRead) {
            provider.markAsRead(notification.id);
          }
          _showNotificationDetails(notification);
        },
        child: Column(
          children: [
            // Header Bar للديون المتأخرة
            if (isDebtNotification) _buildDebtNotificationHeader(notification),

            // Content
            Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: _getTypeColor(
                            notification.type,
                          ).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          notification.type.icon,
                          style: TextStyle(
                            color: _getTypeColor(notification.type),
                            fontSize: 20,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          children: [
                            Text(
                              notification.title,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: notification.isRead
                                    ? Colors.grey.shade600
                                    : Colors.black87,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              notification.message,
                              style: TextStyle(
                                fontSize: 13,
                                color: notification.isRead
                                    ? Colors.grey.shade500
                                    : Colors.grey.shade700,
                              ),
                            ),
                          ],
                        ),
                      ),
                      if (!notification.isRead)
                        Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: _getPriorityColor(notification.priority),
                            shape: BoxShape.circle,
                          ),
                        ),
                    ],
                  ),

                  // معلومات إضافية للديون
                  if (isDebtNotification) ...[
                    const SizedBox(height: 12),
                    _buildEnhancedDebtDetails(notification),

                    // تاريخ القيد ونوع الكارت
                    const SizedBox(height: 8),
                    _buildDebtExtraInfo(notification),
                  ],

                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Icon(
                        Icons.access_time,
                        size: 14,
                        color: Colors.grey.shade500,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        _formatDateTime(notification.createdAt),
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade500,
                        ),
                      ),
                      const Spacer(),
                      if (notification.priority == NotificationPriority.urgent)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.red.shade100,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            'عاجل',
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                              color: Colors.red.shade700,
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Header للديون المتأخرة
  Widget _buildDebtNotificationHeader(NotificationModel notification) {
    Color headerColor;
    IconData headerIcon;
    String statusText;

    switch (notification.type) {
      case NotificationType.overdue:
        headerColor = Colors.red;
        headerIcon = Icons.warning;
        statusText = 'دين متأخر';
        break;
      case NotificationType.dueToday:
        headerColor = Colors.orange;
        headerIcon = Icons.today;
        statusText = 'مستحق اليوم';
        break;
      case NotificationType.dueSoon:
        headerColor = Colors.blue;
        headerIcon = Icons.schedule;
        statusText = 'مستحق قريباً';
        break;
      default:
        headerColor = Colors.grey;
        headerIcon = Icons.info;
        statusText = 'تنبيه';
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [headerColor, headerColor.withValues(alpha: 0.8)],
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Icon(headerIcon, color: Colors.white, size: 20),
          const SizedBox(width: 8),
          Text(
            statusText,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          if (notification.additionalData?['debtCount'] != null) ...[
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${notification.additionalData!['debtCount']} ديون',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  // تفاصيل محسنة للديون
  Widget _buildEnhancedDebtDetails(NotificationModel notification) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        children: [
          // الصف الأول: المبلغ مقابل أقصى تأخير
          Row(
            children: [
              Expanded(
                child: _buildQuickInfoItem(
                  icon: Icons.attach_money,
                  label: 'المبلغ',
                  value: NumberFormatter.formatCurrency(
                    notification.amount ?? 0,
                  ),
                  color: Colors.green,
                ),
              ),
              if (notification.additionalData?['maxDaysOverdue'] != null &&
                  notification.additionalData!['maxDaysOverdue'] > 0) ...[
                const SizedBox(width: 12),
                Expanded(
                  child: _buildQuickInfoItem(
                    icon: Icons.warning,
                    label: 'أقصى تأخير',
                    value:
                        '${notification.additionalData!['maxDaysOverdue']} يوم',
                    color: Colors.red,
                  ),
                ),
              ],
            ],
          ),

          // الصف الثاني: عدد الديون وتاريخ الاستحقاق
          const SizedBox(height: 8),
          Row(
            children: [
              if (notification.additionalData?['debtCount'] != null &&
                  notification.additionalData!['debtCount'] > 1) ...[
                Expanded(
                  child: _buildQuickInfoItem(
                    icon: Icons.format_list_numbered,
                    label: 'عدد الديون',
                    value: '${notification.additionalData!['debtCount']}',
                    color: Colors.orange,
                  ),
                ),
                const SizedBox(width: 12),
              ],
              Expanded(
                child: _buildQuickInfoItem(
                  icon: Icons.calendar_today,
                  label: 'تاريخ الاستحقاق',
                  value: _formatFullDueDateWithTime(notification.dueDate),
                  color: Colors.red,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // عنصر معلومات سريع
  Widget _buildQuickInfoItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(icon, color: color, size: 16),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }

  // معلومات إضافية للدين (تاريخ القيد ونوع الكارت)
  Widget _buildDebtExtraInfo(NotificationModel notification) {
    final List<Widget> infoItems = [];

    // تاريخ القيد
    if (notification.additionalData?['createdAt'] != null) {
      final createdDate = DateTime.parse(
        notification.additionalData!['createdAt'],
      );
      infoItems.add(
        _buildCompactInfoItem(
          icon: Icons.add_circle,
          label: 'تاريخ القيد',
          value: _formatFullCreatedDateWithTime(createdDate),
          color: Colors.blue,
        ),
      );
    } else if (notification.additionalData?['debts'] != null) {
      // للتنبيهات المجمعة - أقدم تاريخ قيد
      final debts = notification.additionalData!['debts'] as List<dynamic>;
      DateTime? oldestDate;
      for (final debt in debts) {
        if (debt['createdAt'] != null) {
          final date = DateTime.parse(debt['createdAt']);
          if (oldestDate == null || date.isBefore(oldestDate)) {
            oldestDate = date;
          }
        }
      }
      if (oldestDate != null) {
        infoItems.add(
          _buildCompactInfoItem(
            icon: Icons.add_circle,
            label: 'أقدم قيد',
            value: _formatFullCreatedDateWithTime(oldestDate),
            color: Colors.blue,
          ),
        );
      }
    }

    // نوع الكارت
    if (notification.additionalData?['cardType'] != null) {
      infoItems.add(
        _buildCompactInfoItem(
          icon: Icons.credit_card,
          label: 'نوع البطاقة',
          value: _getCardTypeDisplayName(
            notification.additionalData!['cardType'].toString(),
          ),
          color: Colors.green,
        ),
      );
    } else if (notification.additionalData?['debts'] != null) {
      // للتنبيهات المجمعة - عرض أنواع البطاقات
      final debts = notification.additionalData!['debts'] as List<dynamic>;
      final cardTypes = <String>{};
      for (final debt in debts) {
        if (debt['cardType'] != null) {
          cardTypes.add(debt['cardType'].toString());
        }
      }
      if (cardTypes.isNotEmpty) {
        final displayText = cardTypes.length == 1
            ? _getCardTypeDisplayName(cardTypes.first)
            : 'أنواع متعددة (${cardTypes.length})';
        infoItems.add(
          _buildCompactInfoItem(
            icon: Icons.credit_card,
            label: 'البطاقات',
            value: displayText,
            color: Colors.green,
          ),
        );
      }
    }

    if (infoItems.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          for (int i = 0; i < infoItems.length; i++) ...[
            Expanded(child: infoItems[i]),
            if (i < infoItems.length - 1) const SizedBox(width: 12),
          ],
        ],
      ),
    );
  }

  // عنصر معلومات مضغوط
  Widget _buildCompactInfoItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: color, size: 14),
            const SizedBox(width: 6),
            Flexible(
              child: Text(
                label,
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 11,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
          maxLines: 3,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  // تنسيق تاريخ الاستحقاق الكامل مع الوقت
  String _formatFullDueDateWithTime(DateTime? dueDate) {
    if (dueDate == null) return 'غير محدد';

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final dueDateOnly = DateTime(dueDate.year, dueDate.month, dueDate.day);
    final difference = dueDateOnly.difference(today).inDays;

    // تحديد اسم اليوم
    String dayName;
    if (difference == 0) {
      dayName = 'اليوم';
    } else if (difference == 1) {
      dayName = 'غداً';
    } else if (difference == -1) {
      dayName = 'أمس';
    } else {
      // أسماء الأيام بالعربية
      const arabicDays = [
        'الاثنين',
        'الثلاثاء',
        'الأربعاء',
        'الخميس',
        'الجمعة',
        'السبت',
        'الأحد',
      ];
      dayName = arabicDays[dueDate.weekday - 1];
    }

    // تنسيق التاريخ
    final dateStr = DateFormat('dd/MM/yyyy').format(dueDate);

    // إرجاع التاريخ بدون وقت
    return '$dayName $dateStr';
  }

  // تنسيق تاريخ القيد الكامل مع الوقت
  String _formatFullCreatedDateWithTime(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final dateOnly = DateTime(date.year, date.month, date.day);
    final difference = today.difference(dateOnly).inDays;

    // تحديد اسم اليوم
    String dayName;
    if (difference == 0) {
      dayName = 'اليوم';
    } else if (difference == 1) {
      dayName = 'أمس';
    } else if (difference == -1) {
      dayName = 'غداً';
    } else {
      // أسماء الأيام بالعربية
      const arabicDays = [
        'الاثنين',
        'الثلاثاء',
        'الأربعاء',
        'الخميس',
        'الجمعة',
        'السبت',
        'الأحد',
      ];
      dayName = arabicDays[date.weekday - 1];
    }

    // تنسيق التاريخ
    final dateStr = DateFormat('dd/MM/yyyy').format(date);

    // تنسيق الوقت بصيغة 12 ساعة
    final hour12 = date.hour == 0
        ? 12
        : date.hour > 12
            ? date.hour - 12
            : date.hour;
    final period = date.hour < 12 ? 'صباحاً' : 'مساءً';
    final timeStr =
        '${hour12.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')} $period';

    return '$dayName $dateStr $timeStr';
  }

  Widget _buildEmptyState({String? message}) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.notifications_off, size: 80, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            message ?? 'لا توجد تنبيهات',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اسحب للأسفل للتحديث',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }

  void _showNotificationDetails(NotificationModel notification) {
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black.withValues(alpha: 0.7),
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        elevation: 20,
        backgroundColor: Colors.transparent,
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          constraints: const BoxConstraints(maxWidth: 600, maxHeight: 650),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 30,
                offset: const Offset(0, 15),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header with gradient - مصغر
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      _getPriorityColor(notification.priority),
                      _getPriorityColor(
                        notification.priority,
                      ).withValues(alpha: 0.8),
                    ],
                  ),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                ),
                child: Row(
                  children: [
                    // Priority Icon - مصغر
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        notification.type.icon,
                        style: const TextStyle(
                          fontSize: 20,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            notification.title,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              notification.priority.displayName,
                              style: const TextStyle(
                                fontSize: 10,
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // Content
              Flexible(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Message
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade50,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.grey.shade200),
                        ),
                        child: Text(
                          notification.message,
                          style: const TextStyle(
                            fontSize: 16,
                            color: Colors.black87,
                            height: 1.5,
                          ),
                        ),
                      ),

                      const SizedBox(height: 24),

                      // Details Section
                      _buildDetailsSection(notification),

                      const SizedBox(height: 20),

                      // Grouped Debts Section (للتنبيهات المجمعة)
                      if (notification.additionalData?['debts'] != null &&
                          (notification.additionalData!['debts'] as List)
                                  .length >
                              1)
                        _buildGroupedDebtsSection(notification),
                    ],
                  ),
                ),
              ),

              // Footer Actions
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(20),
                    bottomRight: Radius.circular(20),
                  ),
                ),
                child: Row(
                  children: [
                    // Time stamp
                    Expanded(
                      child: Row(
                        children: [
                          Icon(
                            Icons.access_time,
                            size: 16,
                            color: Colors.grey[600],
                          ),
                          const SizedBox(width: 8),
                          Text(
                            _formatDateTime(notification.createdAt),
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Action buttons
                    ElevatedButton(
                      onPressed: () => Navigator.pop(context),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _getPriorityColor(
                          notification.priority,
                        ),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'إغلاق',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailsSection(NotificationModel notification) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Title
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.info_outline,
                  color: Colors.blue.shade600,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'تفاصيل التنبيه',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2C3E50),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Grid Layout for Cards
          LayoutBuilder(
            builder: (context, constraints) {
              return _buildResponsiveGrid(
                _getDetailCards(notification),
                constraints.maxWidth,
              );
            },
          ),
        ],
      ),
    );
  }

  List<Widget> _getDetailCards(NotificationModel notification) {
    final List<Widget> detailCards = [];

    // الصف الأول: اسم العميل مقابل المبلغ
    if (notification.customerName != null) {
      detailCards.add(
        _buildEnhancedDetailRow(
          icon: Icons.person,
          iconColor: Colors.blue,
          label: 'اسم العميل',
          value: notification.customerName!,
        ),
      );
    }

    if (notification.amount != null) {
      detailCards.add(
        _buildEnhancedDetailRow(
          icon: Icons.attach_money,
          iconColor: Colors.green,
          label: 'المبلغ الإجمالي',
          value: NumberFormatter.formatCurrency(notification.amount!),
        ),
      );
    }

    // إذا كان تنبيه مجمع، عرض عدد الديون
    if (notification.additionalData?['debtCount'] != null) {
      final debtCount = notification.additionalData!['debtCount'] as int;
      if (debtCount > 1) {
        detailCards.add(
          _buildEnhancedDetailRow(
            icon: Icons.format_list_numbered,
            iconColor: Colors.orange,
            label: 'عدد الديون',
            value: '$debtCount ديون',
          ),
        );
      }
    }

    // الصف الثاني: نوع التنبيه مقابل أقصى تأخير (للديون المتأخرة)
    detailCards.add(
      _buildEnhancedDetailRow(
        icon: Icons.category,
        iconColor: _getTypeColor(notification.type),
        label: 'نوع التنبيه',
        value: notification.type.displayName,
      ),
    );

    if (notification.additionalData?['maxDaysOverdue'] != null) {
      final maxDaysOverdue =
          notification.additionalData!['maxDaysOverdue'] as int;
      if (maxDaysOverdue > 0) {
        detailCards.add(
          _buildEnhancedDetailRow(
            icon: Icons.warning,
            iconColor: Colors.red,
            label: 'أقصى تأخير',
            value: '$maxDaysOverdue يوم',
          ),
        );
      }
    }

    // معلومات التاريخ - عرض دائماً
    if (notification.additionalData?['debts'] == null ||
        (notification.additionalData!['debts'] as List).length == 1) {
      // تنبيه لدين واحد - عرض التفاصيل التقليدية
      if (notification.additionalData?['createdAt'] != null) {
        final createdDate = DateTime.parse(
          notification.additionalData!['createdAt'],
        );
        detailCards.add(
          _buildWideDetailCard(
            icon: Icons.add_circle,
            iconColor: Colors.blue,
            label: 'تاريخ القيد',
            value:
                '${DateFormat('yyyy/MM/dd').format(createdDate)} - ${DateFormat('EEEE', 'ar').format(createdDate)}',
            extra:
                'منذ ${_calculateDaysSince(createdDate)} يوم - ${DateFormat('HH:mm').format(createdDate)}',
          ),
        );
      }

      if (notification.dueDate != null) {
        detailCards.add(
          _buildWideDetailCard(
            icon: Icons.calendar_today,
            iconColor: Colors.red,
            label: 'تاريخ الاستحقاق',
            value:
                '${DateFormat('yyyy/MM/dd').format(notification.dueDate!)} - ${DateFormat('EEEE', 'ar').format(notification.dueDate!)}',
            extra: _getDueDateStatus(notification.dueDate!),
          ),
        );
      }

      if (notification.additionalData?['cardType'] != null) {
        detailCards.add(
          _buildEnhancedDetailRow(
            icon: Icons.credit_card,
            iconColor: Colors.green,
            label: 'نوع البطاقة',
            value: _getCardTypeDisplayName(
              notification.additionalData!['cardType'].toString(),
            ),
          ),
        );
      }
    } else {
      // تنبيه مجمع - عرض معلومات عامة
      if (notification.dueDate != null) {
        detailCards.add(
          _buildWideDetailCard(
            icon: Icons.calendar_today,
            iconColor: Colors.red,
            label: 'تاريخ الاستحقاق',
            value:
                '${DateFormat('yyyy/MM/dd').format(notification.dueDate!)} - ${DateFormat('EEEE', 'ar').format(notification.dueDate!)}',
            extra: _getDueDateStatus(notification.dueDate!),
          ),
        );
      }

      // عرض تاريخ القيد لأقدم دين في المجموعة
      final debts = notification.additionalData!['debts'] as List<dynamic>;
      if (debts.isNotEmpty) {
        // البحث عن أقدم تاريخ قيد
        DateTime? oldestCreatedDate;
        for (final debt in debts) {
          if (debt['createdAt'] != null) {
            final createdDate = DateTime.parse(debt['createdAt']);
            if (oldestCreatedDate == null ||
                createdDate.isBefore(oldestCreatedDate)) {
              oldestCreatedDate = createdDate;
            }
          }
        }

        if (oldestCreatedDate != null) {
          detailCards.add(
            _buildWideDetailCard(
              icon: Icons.add_circle,
              iconColor: Colors.blue,
              label: 'أقدم تاريخ قيد',
              value:
                  '${DateFormat('yyyy/MM/dd').format(oldestCreatedDate)} - ${DateFormat('EEEE', 'ar').format(oldestCreatedDate)}',
              extra:
                  'منذ ${_calculateDaysSince(oldestCreatedDate)} يوم - ${DateFormat('HH:mm').format(oldestCreatedDate)}',
            ),
          );
        }
      }
    }

    return detailCards;
  }

  Widget _buildResponsiveGrid(List<Widget> cards, double maxWidth) {
    if (cards.isEmpty) return const SizedBox.shrink();

    final List<Widget> rows = [];

    for (int i = 0; i < cards.length; i++) {
      final card = cards[i];

      // تحقق إذا كانت البطاقة عريضة (SizedBox مع width: double.infinity)
      if (card is SizedBox && card.width == double.infinity) {
        // بطاقة عريضة - تأخذ العرض الكامل
        rows.add(card);
        if (i < cards.length - 1) {
          rows.add(const SizedBox(height: 8));
        }
      } else {
        // بطاقات عادية - عمودين
        final List<Widget> rowChildren = [];

        // البطاقة الأولى
        rowChildren.add(
          Expanded(
            child: Container(
              constraints: const BoxConstraints(minHeight: 70),
              child: card,
            ),
          ),
        );

        // مسافة بين البطاقات
        rowChildren.add(const SizedBox(width: 8));

        // البطاقة الثانية إذا متوفرة
        if (i + 1 < cards.length) {
          final nextCard = cards[i + 1];
          if (nextCard is SizedBox && nextCard.width == double.infinity) {
            // البطاقة التالية عريضة، أضف مساحة فارغة
            rowChildren.add(const Expanded(child: SizedBox.shrink()));
          } else {
            // بطاقة عادية
            rowChildren.add(
              Expanded(
                child: Container(
                  constraints: const BoxConstraints(minHeight: 70),
                  child: nextCard,
                ),
              ),
            );
            i++; // تخطي البطاقة التالية لأننا أضفناها
          }
        } else {
          // لا توجد بطاقة ثانية
          rowChildren.add(const Expanded(child: SizedBox.shrink()));
        }

        rows.add(
          IntrinsicHeight(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: rowChildren,
            ),
          ),
        );

        if (i < cards.length - 1) {
          rows.add(const SizedBox(height: 6));
        }
      }
    }

    return Column(children: rows);
  }

  Widget _buildGroupedDebtsSection(NotificationModel notification) {
    final debts = notification.additionalData!['debts'] as List<dynamic>;

    return Container(
      margin: const EdgeInsets.only(top: 20),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Title
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.list_alt,
                  color: Colors.red.shade600,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'تفاصيل الديون (${debts.length})',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2C3E50),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // قائمة الديون
          ...debts.asMap().entries.map((entry) {
            final index = entry.key;
            final debt = entry.value as Map<String, dynamic>;

            return Container(
              margin: EdgeInsets.only(
                bottom: index < debts.length - 1 ? 12 : 0,
              ),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // رقم الدين والمبلغ
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade100,
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Text(
                          'دين ${index + 1}',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue.shade700,
                          ),
                        ),
                      ),
                      const Spacer(),
                      Text(
                        NumberFormatter.formatCurrency(
                          debt['amount']?.toDouble() ?? 0,
                        ),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  // نوع البطاقة
                  if (debt['cardType'] != null) ...[
                    Row(
                      children: [
                        Icon(
                          Icons.credit_card,
                          size: 16,
                          color: Colors.grey.shade600,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          'نوع البطاقة: ${_getCardTypeDisplayName(debt['cardType'].toString())}',
                          style: TextStyle(
                            fontSize: 13,
                            color: Colors.grey.shade700,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 6),
                  ],

                  // تاريخ الاستحقاق وحالة التأخير
                  if (debt['dueDate'] != null) ...[
                    Row(
                      children: [
                        Icon(
                          Icons.calendar_today,
                          size: 16,
                          color: Colors.grey.shade600,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          'الاستحقاق: ${_formatFullDate(DateTime.parse(debt['dueDate']))}',
                          style: TextStyle(
                            fontSize: 13,
                            color: Colors.grey.shade700,
                          ),
                        ),
                        const Spacer(),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: _getDaysStatusColor(
                              debt['daysDifference'] ?? 0,
                            ),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            _getDaysStatusText(debt['daysDifference'] ?? 0),
                            style: const TextStyle(
                              fontSize: 11,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],

                  // الملاحظات إذا متوفرة
                  if (debt['notes'] != null &&
                      debt['notes'].toString().isNotEmpty) ...[
                    const SizedBox(height: 6),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Icon(Icons.note, size: 16, color: Colors.grey.shade600),
                        const SizedBox(width: 6),
                        Expanded(
                          child: Text(
                            'ملاحظات: ${debt['notes']}',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildEnhancedDetailRow({
    required IconData icon,
    required Color iconColor,
    required String label,
    required String value,
    String? extra,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // الأيقونة والعنوان
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(3),
                decoration: BoxDecoration(
                  color: iconColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(3),
                ),
                child: Icon(icon, color: iconColor, size: 12),
              ),
              const SizedBox(width: 4),
              Flexible(
                child: Text(
                  label,
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[700],
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),

          // القيمة الرئيسية
          Text(
            value,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
              height: 1.2,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),

          // المعلومات الإضافية
          if (extra != null) ...[
            const SizedBox(height: 2),
            Text(
              extra,
              style: TextStyle(
                fontSize: 8,
                color: Colors.grey[600],
                fontStyle: FontStyle.italic,
                height: 1.1,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      ),
    );
  }

  // بطاقة تفاصيل واسعة للتواريخ
  Widget _buildWideDetailCard({
    required IconData icon,
    required Color iconColor,
    required String label,
    required String value,
    String? extra,
  }) {
    return SizedBox(
      width: double.infinity,
      child: Container(
        padding: const EdgeInsets.all(20),
        margin: const EdgeInsets.only(bottom: 12),
        decoration: BoxDecoration(
          color: icon == Icons.calendar_today
              ? Colors.white
              : iconColor.withValues(alpha: 0.03),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: icon == Icons.calendar_today
                ? Colors.grey.shade300
                : iconColor.withValues(alpha: 0.2),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // الأيقونة والعنوان
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: icon == Icons.calendar_today
                        ? Colors.grey.shade100
                        : iconColor.withValues(alpha: 0.15),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: icon == Icons.calendar_today
                        ? Colors.grey.shade600
                        : iconColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    label,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: icon == Icons.calendar_today
                          ? Colors.grey.shade700
                          : iconColor.withValues(alpha: 0.8),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // القيمة الرئيسية
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.8),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: icon == Icons.calendar_today
                      ? Colors.grey.shade800
                      : Colors.black87,
                  height: 1.3,
                ),
                textAlign: TextAlign.center,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ),

            // المعلومات الإضافية
            if (extra != null) ...[
              const SizedBox(height: 12),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: icon == Icons.calendar_today
                      ? Colors.grey.shade50
                      : iconColor.withValues(alpha: 0.08),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  extra,
                  style: TextStyle(
                    fontSize: 14,
                    color: icon == Icons.calendar_today
                        ? Colors.grey.shade600
                        : iconColor.withValues(alpha: 0.9),
                    fontWeight: FontWeight.w500,
                    fontStyle: FontStyle.italic,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _getDueDateStatus(DateTime dueDate) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final compareDate = DateTime(dueDate.year, dueDate.month, dueDate.day);
    final difference = compareDate.difference(today).inDays;

    if (difference < 0) {
      return 'متأخر ${-difference} يوم';
    } else if (difference == 0) {
      return 'مستحق اليوم';
    } else {
      return 'باقي $difference يوم';
    }
  }

  String _getCardTypeDisplayName(String cardType) {
    // استخدام CardTypeProvider للحصول على الاسم الصحيح
    final cardTypeProvider = Provider.of<CardTypeProvider>(
      context,
      listen: false,
    );

    // أولاً، نحاول البحث بالمعرف المباشر
    final CardTypeOption? cardTypeOption = cardTypeProvider.getCardTypeById(
      cardType,
    );

    // إذا لم نجد، نحاول البحث في الأنواع الافتراضية
    if (cardTypeOption == null) {
      try {
        final defaultType = CardType.values.firstWhere(
          (type) => type.name == cardType,
        );
        return defaultType.displayName;
      } catch (e) {
        // إذا لم يوجد في الأنواع الافتراضية، ابحث في الأنواع المخصصة
        try {
          final customType = cardTypeProvider.customCardTypes.firstWhere(
            (type) => 'custom_${type.id}' == cardType,
          );
          return customType.displayName;
        } catch (e) {
          // إذا لم يوجد في أي مكان، أرجع الـ ID كما هو
          return cardType;
        }
      }
    }

    return cardTypeOption.displayName;
  }

  Color _getTypeColor(NotificationType type) {
    switch (type) {
      case NotificationType.overdue:
        return Colors.red;
      case NotificationType.dueToday:
        return Colors.orange;
      case NotificationType.dueSoon:
        return Colors.blue;
      case NotificationType.lowStock:
        return Colors.orange;
      case NotificationType.outOfStock:
        return Colors.red;
      case NotificationType.payment:
        return Colors.green;
      case NotificationType.general:
        return Colors.grey;
    }
  }

  Color _getPriorityColor(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.urgent:
        return Colors.red;
      case NotificationPriority.high:
        return Colors.orange;
      case NotificationPriority.medium:
        return Colors.blue;
      case NotificationPriority.low:
        return Colors.grey;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays == 0) {
      return 'اليوم ${DateFormat('HH:mm').format(dateTime)}';
    } else if (difference.inDays == 1) {
      return 'أمس ${DateFormat('HH:mm').format(dateTime)}';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} أيام ${DateFormat('HH:mm').format(dateTime)}';
    } else {
      return DateFormat('yyyy/MM/dd HH:mm').format(dateTime);
    }
  }

  // تنسيق التاريخ بالأرقام فقط
  String _formatFullDate(DateTime dateTime) {
    return '${dateTime.year}/${dateTime.month}/${dateTime.day}';
  }

  // حساب عدد الأيام منذ تاريخ معين
  int _calculateDaysSince(DateTime fromDate) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final compareDate = DateTime(fromDate.year, fromDate.month, fromDate.day);

    return today.difference(compareDate).inDays;
  }

  // تحديد لون حالة الأيام
  Color _getDaysStatusColor(int daysDifference) {
    if (daysDifference < 0) {
      return Colors.red; // متأخر
    } else if (daysDifference == 0) {
      return Colors.orange; // مستحق اليوم
    } else {
      return Colors.blue; // مستحق قريباً
    }
  }

  // تحديد نص حالة الأيام
  String _getDaysStatusText(int daysDifference) {
    if (daysDifference < 0) {
      return 'متأخر ${-daysDifference} يوم';
    } else if (daysDifference == 0) {
      return 'مستحق اليوم';
    } else {
      return 'باقي $daysDifference يوم';
    }
  }
}

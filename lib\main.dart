import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'providers/customer_provider.dart';
import 'providers/debt_provider.dart';
import 'providers/customer_statistics_provider.dart';
import 'providers/card_type_provider.dart';
import 'providers/card_profit_provider.dart';
import 'providers/card_inventory_provider.dart';
import 'providers/notification_provider.dart';
import 'providers/smart_notification_provider.dart';
import 'providers/form_data_provider.dart';
import 'providers/font_provider.dart';
import 'screens/splash_screen.dart';

void main() async {
  // Force rebuild - Version 4 with UI improvements
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // تهيئة بسيطة بدون خدمات معقدة
    debugPrint('🚀 بدء تشغيل التطبيق...');

    // Initialize Arabic locale for date formatting
    await initializeDateFormatting('ar');
    debugPrint('✅ تم تهيئة اللغة العربية');

    debugPrint('✅ تم تهيئة التطبيق بنجاح');
    runApp(const MyApp());
  } catch (e, stackTrace) {
    debugPrint('❌ خطأ في تهيئة التطبيق: $e');
    debugPrint('Stack trace: $stackTrace');

    // تشغيل التطبيق مع معالجة الأخطاء
    runApp(ErrorApp(error: e.toString()));
  }
}

// تطبيق معالجة الأخطاء
class ErrorApp extends StatelessWidget {
  const ErrorApp({super.key, required this.error});
  final String error;

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'خطأ في التطبيق',
      home: Scaffold(
        backgroundColor: Colors.red[50],
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 80, color: Colors.red[600]),
                const SizedBox(height: 20),
                Text(
                  'حدث خطأ في تشغيل التطبيق',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.red[800],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 10),
                Text(
                  'يرجى إعادة تشغيل التطبيق',
                  style: TextStyle(fontSize: 16, color: Colors.red[600]),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
                Container(
                  padding: const EdgeInsets.all(15),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: Text(
                    'تفاصيل الخطأ:\n$error',
                    style: const TextStyle(
                      fontSize: 12,
                      fontFamily: 'monospace',
                    ),
                    textAlign: TextAlign.right,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // Customer Provider مع تحميل البيانات
        ChangeNotifierProvider(
          create: (_) {
            final provider = CustomerProvider();
            WidgetsBinding.instance.addPostFrameCallback((_) async {
              try {
                await provider.loadCustomers();
              } catch (e) {
                debugPrint('خطأ في تحميل العملاء: $e');
              }
            });
            return provider;
          },
        ),

        // Debt Provider مع تحميل البيانات
        ChangeNotifierProvider(
          create: (_) {
            final provider = DebtProvider();
            WidgetsBinding.instance.addPostFrameCallback((_) async {
              try {
                await provider.loadAllDebts();
              } catch (e) {
                debugPrint('خطأ في تحميل الديون: $e');
              }
            });
            return provider;
          },
        ),

        // Customer Statistics Provider مبسط
        ChangeNotifierProxyProvider<DebtProvider, CustomerStatisticsProvider>(
          create: (context) => CustomerStatisticsProvider(
            Provider.of<DebtProvider>(context, listen: false),
          ),
          update: (context, debtProvider, previous) =>
              previous ?? CustomerStatisticsProvider(debtProvider),
        ),

        // باقي Providers مع تحميل البيانات
        ChangeNotifierProvider(
          create: (_) {
            final provider = CardTypeProvider();
            WidgetsBinding.instance.addPostFrameCallback((_) async {
              try {
                await provider.loadCustomCardTypes();
              } catch (e) {
                debugPrint('خطأ في تحميل أنواع البطاقات: $e');
              }
            });
            return provider;
          },
        ),
        ChangeNotifierProvider(create: (_) => CardProfitProvider()),
        ChangeNotifierProvider(
          create: (_) {
            final provider = CardInventoryProvider();
            WidgetsBinding.instance.addPostFrameCallback((_) async {
              try {
                await provider.loadInventories();
              } catch (e) {
                debugPrint('خطأ في تحميل المخزون: $e');
              }
            });
            return provider;
          },
        ),
        ChangeNotifierProvider(
          create: (_) {
            final provider = NotificationProvider();
            WidgetsBinding.instance.addPostFrameCallback((_) async {
              try {
                await provider.loadSettings();
              } catch (e) {
                debugPrint('خطأ في تحميل إعدادات الإشعارات: $e');
              }
            });
            return provider;
          },
        ),
        ChangeNotifierProvider(create: (_) => SmartNotificationProvider()),
        ChangeNotifierProvider(
          create: (_) {
            final provider = FormDataProvider();
            WidgetsBinding.instance.addPostFrameCallback((_) async {
              try {
                await provider.loadFormData();
              } catch (e) {
                debugPrint('خطأ في تحميل بيانات النماذج: $e');
              }
            });
            return provider;
          },
        ),
        ChangeNotifierProvider(
          create: (_) {
            final provider = FontProvider();
            WidgetsBinding.instance.addPostFrameCallback((_) async {
              try {
                await provider.loadSettings();
              } catch (e) {
                debugPrint('خطأ في تحميل إعدادات الخط: $e');
              }
            });
            return provider;
          },
        ),
      ],
      child: Consumer<FontProvider>(
        builder: (context, fontProvider, child) {
          try {
            return MaterialApp(
              title: 'محاسب ديون احترافي',
              debugShowCheckedModeBanner: false,
              locale: const Locale('ar', 'SA'),
              localizationsDelegates: const [
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],
              supportedLocales: const [Locale('ar', 'SA'), Locale('en', 'US')],
              theme: fontProvider.getTheme(
                ThemeData(
                  primarySwatch: Colors.teal,
                  primaryColor: const Color(0xFF00695C),
                  colorScheme: ColorScheme.fromSeed(
                    seedColor: const Color(0xFF00695C),
                    primary: const Color(0xFF00695C),
                    secondary: const Color(0xFF4CAF50),
                    tertiary: const Color(0xFF2196F3),
                    surface: const Color(0xFFF8F9FA),
                  ),
                  fontFamily: fontProvider.settings.fontFamily,
                  appBarTheme: const AppBarTheme(
                    backgroundColor: Color(0xFF00695C),
                    foregroundColor: Colors.white,
                    elevation: 0,
                    centerTitle: true,
                    titleTextStyle: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    iconTheme: IconThemeData(color: Colors.white),
                  ),
                  cardTheme: CardThemeData(
                    elevation: 6,
                    shadowColor: Colors.grey.withValues(alpha: 0.3),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    color: Colors.white,
                  ),
                  elevatedButtonTheme: ElevatedButtonThemeData(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF00695C),
                      foregroundColor: Colors.white,
                      elevation: 4,
                      shadowColor: Colors.grey.withValues(alpha: 0.3),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 12,
                      ),
                    ),
                  ),
                  floatingActionButtonTheme:
                      const FloatingActionButtonThemeData(
                    backgroundColor: Color(0xFF4CAF50),
                    foregroundColor: Colors.white,
                    elevation: 8,
                  ),
                  inputDecorationTheme: InputDecorationTheme(
                    filled: true,
                    fillColor: Colors.grey.shade50,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(
                        color: Color(0xFF00695C),
                        width: 2,
                      ),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  useMaterial3: true,
                ),
              ),
              navigatorObservers: [
                // إضافة RouteObserver لمراقبة التنقل
                RouteObserver<PageRoute>(),
              ],
              home: const SplashScreen(),
            );
          } catch (e) {
            debugPrint('❌ خطأ في بناء MaterialApp: $e');
            // إرجاع تطبيق بسيط في حالة الخطأ
            return MaterialApp(
              title: 'محاسب ديون احترافي',
              home: Scaffold(
                body: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error, size: 64, color: Colors.red),
                      const SizedBox(height: 16),
                      const Text(
                        'حدث خطأ في تحميل التطبيق',
                        style: TextStyle(fontSize: 18),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'تفاصيل الخطأ: $e',
                        style: const TextStyle(fontSize: 12),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            );
          }
        },
      ),
    );
  }
}

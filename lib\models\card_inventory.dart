class CardInventory {

  CardInventory({
    this.id,
    required this.cardType,
    required this.quantity,
    this.minQuantity = 10,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  factory CardInventory.fromMap(Map<String, dynamic> map) {
    return CardInventory(
      id: map['id']?.toInt(),
      cardType: map['card_type'] ?? '',
      quantity: map['quantity']?.toInt() ?? 0,
      minQuantity: map['min_quantity']?.toInt() ?? 10,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }
  final int? id;
  final String cardType;
  final int quantity;
  final int minQuantity;
  final DateTime createdAt;
  final DateTime updatedAt;

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'card_type': cardType,
      'quantity': quantity,
      'min_quantity': minQuantity,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  CardInventory copyWith({
    int? id,
    String? cardType,
    int? quantity,
    int? minQuantity,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CardInventory(
      id: id ?? this.id,
      cardType: cardType ?? this.cardType,
      quantity: quantity ?? this.quantity,
      minQuantity: minQuantity ?? this.minQuantity,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // التحقق من نفاد الكمية
  bool get isOutOfStock => quantity <= 0;
  
  // التحقق من انخفاض الكمية
  bool get isLowStock => quantity <= minQuantity && quantity > 0;
  
  // التحقق من توفر الكمية
  bool get isInStock => quantity > minQuantity;

  @override
  String toString() {
    return 'CardInventory{id: $id, cardType: $cardType, quantity: $quantity}';
  }
}

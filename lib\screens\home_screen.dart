import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import '../utils/list_performance_helper.dart';

import '../providers/customer_provider.dart';
import '../providers/debt_provider.dart';
import '../providers/card_inventory_provider.dart';
import '../providers/notification_provider.dart';
import '../providers/smart_notification_provider.dart';
import '../providers/form_data_provider.dart';
import '../providers/card_type_provider.dart';
import '../providers/font_provider.dart';

import '../models/customer.dart';
import '../models/notification_model.dart';

import '../widgets/customer_card.dart';
import '../widgets/search_bar_widget.dart';

import '../widgets/add_debt_bottom_sheet.dart';
import '../services/smart_monitoring_service.dart';
import '../services/backup_service.dart';
import 'card_profit_screen.dart';
import 'card_inventory_screen.dart';
import 'customer_detail_screen.dart';
import 'reports_screen.dart';
import 'statistics_screen.dart';
import 'smart_notifications_screen.dart';
import 'settings_screen.dart';
import 'debts_overview_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with AutomaticKeepAliveClientMixin, WidgetsBindingObserver {
  bool _isSelectionMode = false;
  bool _isSearchMode = false;
  Set<int> _selectedCustomerIds = {};
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  // متغير لتتبع حالة التطبيق
  final bool _wasInBackground = false;

  // NavigatorObserver مخصص لمراقبة التنقل
  late final _HomeNavigatorObserver _navigatorObserver;

  // Timer للتحديث التلقائي للتنبيهات
  Timer? _notificationTimer;

  // متغيرات التبويب - يبدأ بتبويب التسديدات
  int _currentTabIndex = 1;

  @override
  bool get wantKeepAlive => true;

  void _toggleSelectionMode() {
    setState(() {
      _isSelectionMode = !_isSelectionMode;
      if (!_isSelectionMode) {
        _selectedCustomerIds.clear();
      }
    });
  }

  void _selectAllCustomers(List<Customer> customers) {
    setState(() {
      // إذا كانت جميع العملاء محددين، قم بإلغاء التحديد
      if (_selectedCustomerIds.length == customers.length) {
        _selectedCustomerIds.clear();
      } else {
        // وإلا حدد جميع العملاء
        _selectedCustomerIds = customers.map((c) => c.id!).toSet();
      }
    });
  }

  void _deleteSelectedCustomers() async {
    if (_selectedCustomerIds.isEmpty) return;

    final customerProvider = Provider.of<CustomerProvider>(
      context,
      listen: false,
    );

    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.warning_amber_rounded, color: Colors.red[600], size: 28),
            const SizedBox(width: 12),
            const Text(
              'تأكيد الحذف',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        content: Text(
          'هل تريد حذف ${_selectedCustomerIds.length} عميل؟',
          style: const TextStyle(fontSize: 16, color: Colors.black87),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            style: TextButton.styleFrom(foregroundColor: Colors.grey[600]),
            child: const Text('إلغاء', style: TextStyle(fontSize: 16)),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              'حذف',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      // حفظ العدد قبل المسح
      final deletedCount = _selectedCustomerIds.length;

      for (final customerId in _selectedCustomerIds) {
        await customerProvider.deleteCustomer(customerId);
      }
      _toggleSelectionMode();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم حذف $deletedCount عميل'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // تهيئة NavigatorObserver
    _navigatorObserver = _HomeNavigatorObserver(
      onRoutePopped: () {
        // إعادة تعيين البحث عند العودة من أي صفحة
        if (mounted && _isSearchMode) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _resetSearchMode();
          });
        }
      },
    );

    // تحميل البيانات الأساسية فقط أولاً
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadEssentialData();
    });

    // بدء التحديث التلقائي للتنبيهات كل دقيقة
    _startAutoRefreshNotifications();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // عند العودة للتطبيق من الخلفية، إعادة تعيين البحث وتحديث التنبيهات
    if (state == AppLifecycleState.resumed) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          if (_isSearchMode) {
            _resetSearchMode();
          }
          // تحديث فوري للتنبيهات عند العودة للتطبيق
          debugPrint('🔄 تحديث فوري للتنبيهات عند العودة للتطبيق...');
          _refreshNotifications();
        }
      });
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // إعادة تعيين البحث عند العودة للصفحة الرئيسية
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && _isSearchMode) {
        _resetSearchMode();
      }
    });
  }

  // دالة لإعادة تعيين وضع البحث
  void _resetSearchMode() {
    setState(() {
      _isSearchMode = false;
      _searchController.clear();
      _searchFocusNode.unfocus();
      Provider.of<CustomerProvider>(context, listen: false).clearSearch();
    });
  }

  // دالة مساعدة للتنقل مع إعادة تعيين البحث
  Future<T?> _navigateAndResetSearch<T>(Widget destination) async {
    final result = await Navigator.push<T>(
      context,
      MaterialPageRoute(builder: (context) => destination),
    );

    // إعادة تعيين البحث عند العودة - دائماً
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && _isSearchMode) {
        _resetSearchMode();
      }
    });

    return result;
  }

  // دالة للتنقل العادي مع إعادة تعيين البحث
  Future<T?> _navigateToPage<T>(Widget destination) async {
    final result = await Navigator.push<T>(
      context,
      MaterialPageRoute(builder: (context) => destination),
    );

    // إعادة تعيين البحث عند العودة من أي صفحة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && _isSearchMode) {
        _resetSearchMode();
      }
    });

    return result;
  }

  // تحميل البيانات الأساسية بشكل متدرج
  Future<void> _loadEssentialData() async {
    try {
      debugPrint('🔄 بدء تحميل البيانات الأساسية...');

      // تحسين الذاكرة قبل التحميل
      ListPerformanceHelper.optimizeListMemory();

      // تحميل العملاء فقط أولاً (الأهم)
      final customerProvider = Provider.of<CustomerProvider>(
        context,
        listen: false,
      );

      await customerProvider.loadCustomers();
      debugPrint('✅ تم تحميل ${customerProvider.customers.length} عميل');

      // تحميل باقي البيانات في الخلفية
      if (mounted) {
        // تحميل بدون انتظار لتسريع العرض
        unawaited(_loadSecondaryData());
      }
    } catch (e, stackTrace) {
      debugPrint('❌ خطأ في تحميل البيانات الأساسية: $e');
      debugPrint('Stack trace: $stackTrace');

      // محاولة تحميل البيانات الثانوية حتى لو فشل تحميل العملاء
      if (mounted) {
        unawaited(_loadSecondaryData());
      }
    }
  }

  // تحميل البيانات الثانوية في الخلفية
  Future<void> _loadSecondaryData() async {
    try {
      // تحميل الديون والمخزون أولاً (مطلوب للإشعارات)
      final debtProvider = Provider.of<DebtProvider>(context, listen: false);
      final cardInventoryProvider = Provider.of<CardInventoryProvider>(
        context,
        listen: false,
      );

      await Future.wait([
        debtProvider.loadAllDebts(),
        debtProvider.loadAllPayments(), // تحميل التسديدات
        cardInventoryProvider.loadInventories(),
        Provider.of<FontProvider>(context, listen: false).loadSettings(),
        Provider.of<FormDataProvider>(context, listen: false).loadFormData(),
        Provider.of<NotificationProvider>(
          context,
          listen: false,
        ).loadSettings(),
        Provider.of<CardTypeProvider>(
          context,
          listen: false,
        ).loadCustomCardTypes(),
      ]);

      debugPrint('✅ تم تحميل جميع البيانات');
      debugPrint('📊 عدد الديون المحملة: ${debtProvider.debts.length}');
      debugPrint(
        '📦 عدد المخزون المحمل: ${cardInventoryProvider.inventories.length}',
      );

      // تهيئة المراقبة الذكية والإشعارات بعد تحميل البيانات
      if (mounted) {
        _initializeSmartMonitoring();
        _initializeLocalNotifications();
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل البيانات الثانوية: $e');
    }
  }

  void _initializeSmartMonitoring() async {
    try {
      debugPrint('🔧 بدء تهيئة المراقبة الذكية...');

      final smartNotificationProvider = Provider.of<SmartNotificationProvider>(
        context,
        listen: false,
      );
      final debtProvider = Provider.of<DebtProvider>(context, listen: false);
      final customerProvider = Provider.of<CustomerProvider>(
        context,
        listen: false,
      );
      final inventoryProvider = Provider.of<CardInventoryProvider>(
        context,
        listen: false,
      );

      debugPrint('📊 البيانات المتاحة للمراقبة:');
      debugPrint('   - الديون: ${debtProvider.debts.length}');
      debugPrint('   - العملاء: ${customerProvider.customers.length}');
      debugPrint('   - المخزون: ${inventoryProvider.inventories.length}');

      // تهيئة خدمة المراقبة الذكية
      SmartMonitoringService().initialize(
        notificationProvider: smartNotificationProvider,
        debtProvider: debtProvider,
        customerProvider: customerProvider,
        inventoryProvider: inventoryProvider,
      );

      // بدء المراقبة التلقائية
      SmartMonitoringService().startMonitoring();

      // تحديث التنبيهات في الخلفية (بدون انتظار)
      debugPrint('🔄 بدء تحديث التنبيهات الأولي...');
      _refreshNotifications();

      // تحديث فوري إضافي بعد ثانيتين للتأكد
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          debugPrint('🔄 تحديث إضافي للتنبيهات...');
          _refreshNotifications();
        }
      });

      // إنشاء إشعارات الديون فقط بدون إشعارات ترحيبية
      Future.delayed(const Duration(seconds: 2), () async {
        if (mounted) {
          // إنشاء إشعار بناءً على بيانات حقيقية إذا وجدت
          await _createRealDataNotification();

          debugPrint('✅ تم إنشاء إشعارات الديون');
        }
      });

      debugPrint('✅ تم تهيئة المراقبة الذكية بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة المراقبة الذكية: $e');
    }
  }

  // تهيئة خدمة الإشعارات المحلية
  void _initializeLocalNotifications() async {
    try {
      final notificationProvider = Provider.of<NotificationProvider>(
        context,
        listen: false,
      );

      // تهيئة خدمة الإشعارات المحلية
      await notificationProvider.initializeLocalNotifications();

      // تشغيل الإشعارات التلقائية
      _triggerAutomaticNotifications();

      debugPrint('✅ تم تهيئة خدمة الإشعارات المحلية بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة الإشعارات المحلية: $e');
    }
  }

  // تشغيل الإشعارات التلقائية
  void _triggerAutomaticNotifications() async {
    try {
      final smartNotificationProvider = Provider.of<SmartNotificationProvider>(
        context,
        listen: false,
      );
      final debtProvider = Provider.of<DebtProvider>(context, listen: false);
      final customerProvider = Provider.of<CustomerProvider>(
        context,
        listen: false,
      );
      final notificationProvider = Provider.of<NotificationProvider>(
        context,
        listen: false,
      );

      // تحميل الديون إذا لم تكن محملة
      if (debtProvider.debts.isEmpty) {
        await debtProvider.loadAllDebts();
      }

      // تحميل المخزون إذا لم يكن محملاً
      if (!mounted) return;
      final cardInventoryProvider = Provider.of<CardInventoryProvider>(
        context,
        listen: false,
      );
      if (cardInventoryProvider.inventories.isEmpty) {
        await cardInventoryProvider.loadInventories();
      }

      // إنشاء الإشعارات الذكية داخل التطبيق
      await smartNotificationProvider.generateSmartNotifications(
        debtProvider: debtProvider,
        customerProvider: customerProvider,
        inventoryProvider: cardInventoryProvider,
        notificationProvider: notificationProvider,
      );

      // تشغيل الإشعارات المحلية التلقائية
      if (mounted) {
        final cardTypeProvider = Provider.of<CardTypeProvider>(
          context,
          listen: false,
        );
        await smartNotificationProvider.triggerAutomaticNotifications(
          debtProvider: debtProvider,
          customerProvider: customerProvider,
          cardTypeProvider: cardTypeProvider,
          notificationProvider: notificationProvider,
        );
      }

      // لا نحتاج لإشعار الملخص - فقط إشعارات الديون الفردية

      debugPrint('✅ تم إنشاء الإشعارات الذكية وتشغيل الإشعارات التلقائية');
    } catch (e) {
      debugPrint('❌ خطأ في تشغيل الإشعارات التلقائية: $e');
    }
  }

  // بدء التحديث التلقائي للتنبيهات
  void _startAutoRefreshNotifications() {
    // إلغاء Timer السابق إذا كان موجوداً
    _notificationTimer?.cancel();

    // إنشاء Timer جديد يعمل كل 30 ثانية
    _notificationTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (mounted) {
        debugPrint('🔄 تحديث تلقائي للتنبيهات...');
        _refreshNotifications();
      } else {
        timer.cancel();
      }
    });

    debugPrint('✅ تم بدء التحديث التلقائي للتنبيهات كل 30 ثانية');
  }

  // تحديث التنبيهات في الخلفية
  void _refreshNotifications() async {
    try {
      final smartNotificationProvider = Provider.of<SmartNotificationProvider>(
        context,
        listen: false,
      );
      final debtProvider = Provider.of<DebtProvider>(context, listen: false);
      final customerProvider = Provider.of<CustomerProvider>(
        context,
        listen: false,
      );
      final inventoryProvider = Provider.of<CardInventoryProvider>(
        context,
        listen: false,
      );

      // تحديث البيانات بشكل متوازي
      await Future.wait([
        debtProvider.loadAllDebts(),
        // لا نحتاج لإعادة تحميل العملاء لأنهم محملين بالفعل
      ]);

      // إنشاء التنبيهات
      if (mounted) {
        final notificationProvider = Provider.of<NotificationProvider>(
          context,
          listen: false,
        );

        await smartNotificationProvider.generateSmartNotifications(
          debtProvider: debtProvider,
          customerProvider: customerProvider,
          inventoryProvider: inventoryProvider,
          notificationProvider: notificationProvider,
        );

        debugPrint(
          '🔄 تم تحديث التنبيهات - العدد: ${smartNotificationProvider.notifications.length}',
        );
      }
    } catch (e) {
      debugPrint('خطأ في تحديث التنبيهات: $e');
    }
  }

  // إنشاء إشعار من البيانات الحقيقية
  Future<void> _createRealDataNotification() async {
    try {
      final customerProvider = Provider.of<CustomerProvider>(
        context,
        listen: false,
      );
      final debtProvider = Provider.of<DebtProvider>(context, listen: false);
      final smartNotificationProvider = Provider.of<SmartNotificationProvider>(
        context,
        listen: false,
      );

      // الحصول على العملاء والديون
      final customers = customerProvider.customers;
      final debts = debtProvider.debts;

      if (customers.isNotEmpty && debts.isNotEmpty) {
        // البحث عن عميل لديه ديون متأخرة
        for (final customer in customers) {
          final customerDebts =
              debts.where((debt) => debt.customerId == customer.id).toList();

          if (customerDebts.isNotEmpty) {
            // البحث عن ديون متأخرة
            final overdueDebts = customerDebts.where((debt) {
              return debt.dueDate.isBefore(DateTime.now()) &&
                  debt.remainingAmount > 0;
            }).toList();

            if (overdueDebts.isNotEmpty) {
              final totalAmount = overdueDebts.fold<double>(
                0,
                (sum, debt) => sum + debt.remainingAmount,
              );

              final overdueNotification = NotificationModel(
                id: 'real_overdue_${DateTime.now().millisecondsSinceEpoch}',
                title: 'العميل ${customer.name} لديه دين متأخر',
                message:
                    'دين ${customer.name} متأخر - ${_formatAmount(totalAmount)}',
                type: NotificationType.overdue,
                priority: NotificationPriority.urgent,
                createdAt: DateTime.now(),
                customerId: customer.id?.toString(),
                customerName: customer.name,
                amount: totalAmount,
                dueDate: overdueDebts.first.dueDate,
                additionalData: {
                  'customerName': customer.name,
                  'phoneNumber': customer.phone ?? '',
                  'totalAmount': totalAmount,
                  'debtCount': overdueDebts.length,
                  'maxDaysOverdue': DateTime.now()
                      .difference(overdueDebts.first.dueDate)
                      .inDays,
                },
              );

              smartNotificationProvider.addExternalNotification(
                overdueNotification,
              );
              debugPrint('✅ تم إنشاء إشعار حقيقي للعميل: ${customer.name}');
              return; // إنشاء إشعار واحد فقط
            }
          }
        }
      }

      debugPrint('ℹ️ لا توجد ديون متأخرة لإنشاء إشعار حقيقي');
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء إشعار من البيانات الحقيقية: $e');
    }
  }

  // تنسيق المبلغ
  String _formatAmount(double amount) {
    if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)} ألف';
    } else {
      return '${amount.toStringAsFixed(0)} ر.س';
    }
  }

  // اختبار الإشعارات
  Future<void> _testNotifications() async {
    try {
      debugPrint('🧪 بدء اختبار الإشعارات...');

      final smartNotificationProvider = Provider.of<SmartNotificationProvider>(
        context,
        listen: false,
      );

      // إضافة إشعار تجريبي
      final testNotification = NotificationModel(
        id: 'test_${DateTime.now().millisecondsSinceEpoch}',
        title: 'إشعار تجريبي',
        message: 'هذا إشعار تجريبي للتأكد من عمل النظام',
        type: NotificationType.overdue,
        priority: NotificationPriority.high,
        createdAt: DateTime.now(),
      );

      smartNotificationProvider.addExternalNotification(testNotification);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم إضافة إشعار تجريبي! العدد الحالي: ${smartNotificationProvider.notifications.length}',
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
      }

      debugPrint('✅ تم إضافة إشعار تجريبي بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في اختبار الإشعارات: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في اختبار الإشعارات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _toggleSearchMode() {
    setState(() {
      _isSearchMode = !_isSearchMode;
      if (_isSearchMode) {
        // إذا كان في وضع التحديد، أخرج منه
        if (_isSelectionMode) {
          _isSelectionMode = false;
          _selectedCustomerIds.clear();
        }
        // ركز على حقل البحث
        Future.delayed(const Duration(milliseconds: 100), () {
          _searchFocusNode.requestFocus();
        });
      } else {
        // امسح البحث عند الخروج
        _searchController.clear();
        _searchFocusNode.unfocus();
        Provider.of<CustomerProvider>(context, listen: false).clearSearch();
      }
    });
  }

  void _onSearchChanged(String query) {
    Provider.of<CustomerProvider>(
      context,
      listen: false,
    ).searchCustomers(query, searchType: 'all');
  }

  void _showLongPressOptions(Customer customer) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(25)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 50,
              height: 5,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(3),
              ),
            ),

            const SizedBox(height: 20),

            // العنوان مع معلومات العميل
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Colors.blue.shade600,
                              Colors.blue.shade700,
                            ],
                          ),
                          borderRadius: BorderRadius.circular(15),
                        ),
                        child: const Icon(
                          Icons.person,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              customer.name,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                            if (customer.phone != null &&
                                customer.phone!.isNotEmpty)
                              Text(
                                customer.phone!,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  const Divider(),
                ],
              ),
            ),

            const SizedBox(height: 10),

            // خيار البحث الاحترافي - متاح دائماً
            _buildOptionTile(
              icon: Icons.search,
              iconColor: Colors.blue.shade600,
              iconBgColor: Colors.blue.shade100,
              title: 'البحث الاحترافي',
              subtitle: _isSelectionMode
                  ? 'ابحث أثناء التحديد للحذف'
                  : 'ابحث في جميع العملاء بسرعة',
              onTap: () {
                Navigator.pop(context);
                _toggleSearchMode();
              },
            ),

            // خيار عرض تفاصيل العميل
            _buildOptionTile(
              icon: Icons.visibility,
              iconColor: Colors.green.shade600,
              iconBgColor: Colors.green.shade100,
              title: 'عرض تفاصيل العميل',
              subtitle: 'اعرض جميع ديون ومدفوعات ${customer.name}',
              onTap: () {
                Navigator.pop(context);
                _navigateAndResetSearch(
                  CustomerDetailScreen(customer: customer),
                );
              },
            ),

            // خيار إضافة دين جديد
            _buildOptionTile(
              icon: Icons.add_card,
              iconColor: Colors.purple.shade600,
              iconBgColor: Colors.purple.shade100,
              title: 'إضافة دين جديد',
              subtitle: 'أضف دين جديد لـ ${customer.name}',
              onTap: () {
                Navigator.pop(context);
                _showAddDebtBottomSheet(context, customer);
              },
            ),

            // خيارات حسب الوضع الحالي
            if (!_isSelectionMode) ...[
              // خيار التحديد المتعدد - فقط في الوضع العادي
              _buildOptionTile(
                icon: Icons.checklist,
                iconColor: Colors.orange.shade600,
                iconBgColor: Colors.orange.shade100,
                title: 'تحديد متعدد للحذف',
                subtitle: 'حدد عملاء متعددين للحذف الجماعي',
                onTap: () {
                  Navigator.pop(context);
                  if (_isSearchMode) _toggleSearchMode();
                  _toggleSelectionMode();
                  setState(() {
                    _selectedCustomerIds.add(customer.id!);
                  });
                },
              ),
            ] else ...[
              // خيارات وضع التحديد
              _buildOptionTile(
                icon: Icons.add_circle,
                iconColor: Colors.green.shade600,
                iconBgColor: Colors.green.shade100,
                title: 'إضافة للتحديد',
                subtitle: 'أضف ${customer.name} للعملاء المحددين',
                onTap: () {
                  Navigator.pop(context);
                  setState(() {
                    _selectedCustomerIds.add(customer.id!);
                  });
                },
              ),

              _buildOptionTile(
                icon: Icons.exit_to_app,
                iconColor: Colors.grey.shade600,
                iconBgColor: Colors.grey.shade100,
                title: 'إنهاء وضع التحديد',
                subtitle: 'العودة للوضع العادي',
                onTap: () {
                  Navigator.pop(context);
                  _toggleSelectionMode();
                },
              ),
            ],

            // خيار حذف العميل - فقط في الوضع العادي
            if (!_isSelectionMode)
              _buildOptionTile(
                icon: Icons.delete_forever,
                iconColor: Colors.red.shade600,
                iconBgColor: Colors.red.shade100,
                title: 'حذف هذا العميل',
                subtitle: 'حذف ${customer.name} وجميع بياناته نهائياً',
                onTap: () {
                  Navigator.pop(context);
                  _deleteSingleCustomer(customer);
                },
              ),

            const SizedBox(height: 25),
          ],
        ),
      ),
    );
  }

  // بناء عنصر خيار في القائمة
  Widget _buildOptionTile({
    required IconData icon,
    required Color iconColor,
    required Color iconBgColor,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
      leading: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: iconBgColor,
          borderRadius: BorderRadius.circular(15),
        ),
        child: Icon(icon, color: iconColor, size: 24),
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Colors.black87,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
      ),
      onTap: onTap,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
    );
  }

  void _deleteSingleCustomer(Customer customer) async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.red.shade100,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.warning_amber_rounded,
                color: Colors.red.shade600,
                size: 28,
              ),
            ),
            const SizedBox(width: 15),
            const Expanded(
              child: Text(
                'تأكيد الحذف',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ),
          ],
        ),
        content: Container(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'هل تريد حذف العميل "${customer.name}" نهائياً؟',
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.black87,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 10),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Colors.red.shade600,
                      size: 20,
                    ),
                    const SizedBox(width: 10),
                    const Expanded(
                      child: Text(
                        'سيتم حذف جميع الديون والمدفوعات المرتبطة بهذا العميل',
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.red,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            style: TextButton.styleFrom(
              foregroundColor: Colors.grey[600],
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            ),
            child: const Text(
              'إلغاء',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              'حذف نهائي',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      final customerProvider = Provider.of<CustomerProvider>(
        context,
        listen: false,
      );

      await customerProvider.deleteCustomer(customer.id!);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 10),
                Expanded(child: Text('تم حذف العميل "${customer.name}" بنجاح')),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    // إيقاف المراقبة الذكية عند إغلاق الشاشة
    SmartMonitoringService().stopMonitoring();

    // إلغاء Timer التحديث التلقائي
    _notificationTimer?.cancel();

    WidgetsBinding.instance.removeObserver(this);
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return PopScope(
      canPop: !_isSearchMode,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop && _isSearchMode) {
          _resetSearchMode();
        }
      },
      child: GestureDetector(
        onTap: () {
          // إعادة تعيين البحث عند الضغط خارج حقل البحث
          if (_isSearchMode) {
            _resetSearchMode();
          }
        },
        child: Scaffold(
          backgroundColor: const Color(0xFFF5F7FA),
          resizeToAvoidBottomInset: false,
          appBar: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            flexibleSpace: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.blue.shade600,
                    Colors.blue.shade800,
                    Colors.indigo.shade700,
                  ],
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.blue.withValues(alpha: 0.3),
                    blurRadius: 15,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
            ),
            toolbarHeight: 70,
            title: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.white.withValues(alpha: 0.3),
                        Colors.white.withValues(alpha: 0.1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(14),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.3),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 6,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.account_balance_wallet,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 10),
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'محاسب الديون',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          shadows: [
                            Shadow(
                              color: Colors.black26,
                              offset: Offset(0, 1),
                              blurRadius: 2,
                            ),
                          ],
                        ),
                      ),
                      Text(
                        'إدارة متكاملة للمبيعات والأرباح',
                        style: TextStyle(
                          fontSize: 11,
                          color: Colors.white70,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            actions: [
              // التبويبات في الشريط العلوي
              if (!_isSelectionMode && !_isSearchMode) ...[
                _buildTopTabButton('العملاء', Icons.people_rounded, 0),
                const SizedBox(width: 4),
                _buildTopTabButton('التسديدات', Icons.payment_rounded, 1),
                const SizedBox(width: 12),
              ],
              Builder(
                builder: (context) => IconButton(
                  icon: Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.menu,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  onPressed: () => Scaffold.of(context).openEndDrawer(),
                ),
              ),
              const SizedBox(width: 6),
            ],
          ),
          endDrawer: _buildDrawer(),
          body: Column(
            children: [
              // Selection Mode Toolbar
              if (_isSelectionMode)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 10,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    border: Border(
                      bottom: BorderSide(
                        color: Colors.blue.shade200,
                        width: 0.5,
                      ),
                    ),
                  ),
                  child: Row(
                    children: [
                      IconButton(
                        onPressed: _toggleSelectionMode,
                        icon: const Icon(Icons.close, size: 18),
                        tooltip: 'إلغاء التحديد',
                        padding: const EdgeInsets.all(3),
                        constraints: const BoxConstraints(
                          minWidth: 28,
                          minHeight: 28,
                        ),
                      ),
                      const SizedBox(width: 3),
                      Text(
                        'تم تحديد ${_selectedCustomerIds.length} عميل',
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                      const Spacer(),
                      Consumer<CustomerProvider>(
                        builder: (context, customerProvider, _) {
                          return TextButton(
                            onPressed: () =>
                                _selectAllCustomers(customerProvider.customers),
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 3,
                              ),
                              minimumSize: const Size(0, 28),
                            ),
                            child: Text(
                              _selectedCustomerIds.length ==
                                      customerProvider.customers.length
                                  ? 'إلغاء تحديد الكل'
                                  : 'تحديد الكل',
                              style: const TextStyle(
                                fontSize: 10,
                                color: Colors.black87,
                              ),
                            ),
                          );
                        },
                      ),
                      const SizedBox(width: 3),
                      ElevatedButton.icon(
                        onPressed: _selectedCustomerIds.isNotEmpty
                            ? _deleteSelectedCustomers
                            : null,
                        icon: const Icon(Icons.delete, size: 14),
                        label: const Text(
                          'حذف',
                          style: TextStyle(fontSize: 10),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 3,
                          ),
                          minimumSize: const Size(0, 28),
                        ),
                      ),
                    ],
                  ),
                ),

              // Search Bar - عادي أو احترافي
              if (!_isSelectionMode && !_isSearchMode)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  child: SearchBarWidget(
                    onCustomerSelected: (customer) {
                      _navigateAndResetSearch(
                        CustomerDetailScreen(customer: customer),
                      );
                    },
                    onClearSearch: () {
                      // لا حاجة لفعل شيء هنا، البحث العادي لا يحتاج إعادة تعيين
                    },
                  ),
                ),

              // Professional Search Bar - يظهر عند الضغط المطول
              if (_isSearchMode)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  child: _buildProfessionalSearchBar(),
                ),

              // بطاقة الإشعارات الذكية - مخفية حسب طلب المستخدم
              // if (!_isSelectionMode && !_isSearchMode)
              //   _buildSmartNotificationCard(),

              // المحتوى حسب التبويب المختار
              Expanded(
                child: _currentTabIndex == 0
                    ? _buildCustomersTab()
                    : _buildPaymentsTab(),
              ),
            ],
          ),
          bottomNavigationBar: _buildProfessionalBottomBar(),
        ),
      ),
    );
  }

  // تبويب العملاء
  Widget _buildCustomersTab() {
    return Consumer<CustomerProvider>(
      builder: (context, customerProvider, child) {
        if (customerProvider.isLoading) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Colors.blue.shade600,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'جاري تحميل البيانات...',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          );
        }

        if (customerProvider.customers.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.people_outline,
                  size: 80,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  'لا يوجد عملاء',
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'اضغط على زر + لإضافة دين جديد',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
          );
        }

        // مراقبة أداء القائمة
        ListPerformanceHelper.monitorListPerformance(
          'CustomersList',
        );

        return ListView.builder(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          itemCount: customerProvider.customers.length,
          physics: const BouncingScrollPhysics(),
          cacheExtent: 500,
          addAutomaticKeepAlives: false,
          addSemanticIndexes: false,
          itemBuilder: (context, index) {
            final customer = customerProvider.customers[index];
            return FutureBuilder<double>(
              future: customerProvider.getCustomerTotalAmount(
                customer.id!,
              ),
              builder: (context, snapshot) {
                return CustomerCard(
                  customer: customer,
                  index: index,
                  isSelectionMode: _isSelectionMode,
                  isSelected: _selectedCustomerIds.contains(
                    customer.id,
                  ),
                  isSearchMode: _isSearchMode,
                  totalAmount: snapshot.data,
                  onTap: () {
                    if (!_isSelectionMode) {
                      _navigateAndResetSearch(
                        CustomerDetailScreen(customer: customer),
                      );
                    }
                  },
                  onLongPress: () {
                    // إظهار الخيارات في جميع الأوضاع
                    _showLongPressOptions(customer);
                  },
                  onSelectionChanged: (isSelected) {
                    setState(() {
                      if (isSelected) {
                        _selectedCustomerIds.add(customer.id!);
                      } else {
                        _selectedCustomerIds.remove(customer.id!);
                      }
                    });
                  },
                  onDeletePressed: () {
                    _deleteSingleCustomer(customer);
                  },
                  onSelectPressed: () {
                    setState(() {
                      _selectedCustomerIds.add(customer.id!);
                    });
                    if (!_isSelectionMode) {
                      _toggleSelectionMode();
                    }
                  },
                );
              },
            );
          },
        );
      },
    );
  }

  // تبويب التسديدات - يعرض العملاء مع تسديداتهم
  Widget _buildPaymentsTab() {
    return Consumer2<CustomerProvider, DebtProvider>(
      builder: (context, customerProvider, debtProvider, child) {
        if (customerProvider.isLoading || debtProvider.isLoading) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Colors.blue.shade600,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'جاري تحميل البيانات...',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          );
        }

        // عرض جميع العملاء (نفس قائمة العملاء)
        final allCustomers = customerProvider.customers;

        if (allCustomers.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.people_outlined,
                  size: 80,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  'لا يوجد عملاء',
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'أضف عملاء لرؤية تسديداتهم هنا',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          itemCount: allCustomers.length,
          physics: const BouncingScrollPhysics(),
          itemBuilder: (context, index) {
            final customer = allCustomers[index];
            final customerPayments = debtProvider.payments
                .where((payment) => payment.customerId == customer.id)
                .toList();

            return CustomerCard(
              customer: customer,
              index: index,
              totalAmount: customerPayments.fold(
                  0.0, (sum, payment) => (sum ?? 0.0) + payment.amount),
              onTap: () {
                _navigateAndResetSearch(
                  CustomerDetailScreen(customer: customer),
                );
              },
              onLongPress: () {
                _showLongPressOptions(customer);
              },
              onSelectionChanged: (isSelected) {},
              onDeletePressed: () {},
              onSelectPressed: () {},
            );
          },
        );
      },
    );
  }

  // بناء زر التبويب في الشريط العلوي
  Widget _buildTopTabButton(String title, IconData icon, int index) {
    final isSelected = _currentTabIndex == index;
    return InkWell(
      onTap: () {
        setState(() {
          _currentTabIndex = index;
        });
      },
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: isSelected
              ? Colors.white.withValues(alpha: 0.2)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: isSelected
              ? Border.all(color: Colors.white.withValues(alpha: 0.3))
              : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: Colors.white,
              size: 16,
            ),
            const SizedBox(width: 4),
            Text(
              title,
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // شريط تنقل أنيق وبسيط
  Widget _buildProfessionalBottomBar() {
    return Container(
      height: 75,
      margin: const EdgeInsets.only(bottom: 25),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          // الإحصائيات
          Expanded(
            child: _buildBottomNavItem(
              icon: Icons.bar_chart_rounded,
              label: 'الإحصائيات',
              color: Colors.blue.shade600,
              onTap: () {
                _navigateToPage(const StatisticsScreen());
              },
            ),
          ),

          // التقارير
          Expanded(
            child: _buildBottomNavItem(
              icon: Icons.assessment_rounded,
              label: 'التقارير',
              color: Colors.green.shade600,
              onTap: () {
                _navigateToPage(const ReportsScreen());
              },
            ),
          ),

          // زر إضافة الدين
          Expanded(child: _buildAddDebtButton()),

          // المخزون
          Expanded(
            child: _buildBottomNavItem(
              icon: Icons.inventory_2_rounded,
              label: 'المخزون',
              color: Colors.orange.shade600,
              onTap: () {
                _navigateToPage(const CardInventoryScreen());
              },
            ),
          ),

          // الإعدادات
          Expanded(
            child: _buildBottomNavItem(
              icon: Icons.settings_rounded,
              label: 'الإعدادات',
              color: Colors.grey.shade700,
              onTap: () {
                _navigateToPage(const SettingsScreen());
              },
            ),
          ),
        ],
      ),
    );
  }

  // بناء شريط التبويب
  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // تبويب العملاء
          Expanded(
            child: _buildTabItem(
              title: 'العملاء',
              icon: Icons.people_rounded,
              isSelected: _currentTabIndex == 0,
              onTap: () {
                setState(() {
                  _currentTabIndex = 0;
                });
              },
            ),
          ),
          // تبويب التسديدات
          Expanded(
            child: _buildTabItem(
              title: 'التسديدات',
              icon: Icons.payment_rounded,
              isSelected: _currentTabIndex == 1,
              onTap: () {
                setState(() {
                  _currentTabIndex = 1;
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  // بناء عنصر التبويب
  Widget _buildTabItem({
    required String title,
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue.shade600 : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: isSelected ? Colors.white : Colors.grey.shade600,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.grey.shade600,
                fontSize: 14,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // بناء عنصر التنقل السفلي البسيط
  Widget _buildBottomNavItem({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 10),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  // بناء بطاقة الإشعارات الذكية
  Widget _buildSmartNotificationCard() {
    return Consumer<SmartNotificationProvider>(
      builder: (context, notificationProvider, _) {
        // إخفاء البطاقة تماماً عند عدم وجود إشعارات
        if (notificationProvider.notifications.isEmpty) {
          return const SizedBox.shrink();
        }

        final unreadCount = notificationProvider.unreadCount;
        final totalCount = notificationProvider.notifications.length;

        // أحدث 3 إشعارات
        final recentNotifications =
            notificationProvider.notifications.take(3).toList();

        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Colors.orange.shade50, Colors.orange.shade100],
            ),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.orange.shade200),
            boxShadow: [
              BoxShadow(
                color: Colors.orange.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: InkWell(
            onTap: () => _navigateToPage(const SmartNotificationsScreen()),
            borderRadius: BorderRadius.circular(16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.orange.shade600,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: const Icon(
                          Icons.notifications_active,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'التنبيهات الذكية',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF2C3E50),
                              ),
                            ),
                            Row(
                              children: [
                                Text(
                                  '$totalCount تنبيه${unreadCount > 0 ? ' • $unreadCount غير مقروء' : ''}',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.orange.shade700,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                const SizedBox(width: 6),
                                Container(
                                  width: 6,
                                  height: 6,
                                  decoration: BoxDecoration(
                                    color: Colors.green.shade600,
                                    shape: BoxShape.circle,
                                  ),
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'تلقائي',
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: Colors.green.shade600,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      if (unreadCount > 0) ...[
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            '$unreadCount',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        // زر تحديد الكل كمقروء
                        InkWell(
                          onTap: () {
                            final count =
                                notificationProvider.notifications.length;
                            notificationProvider.markAllAsRead();
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  'تم إزالة جميع الإشعارات ($count) من الشاشة',
                                ),
                                backgroundColor: Colors.green,
                                duration: const Duration(seconds: 2),
                                action: SnackBarAction(
                                  label: 'تم ✓',
                                  textColor: Colors.white,
                                  onPressed: () {},
                                ),
                              ),
                            );
                          },
                          borderRadius: BorderRadius.circular(8),
                          child: Container(
                            padding: const EdgeInsets.all(6),
                            decoration: BoxDecoration(
                              color: Colors.green.shade100,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.green.shade300),
                            ),
                            child: Icon(
                              Icons.done_all,
                              color: Colors.green.shade700,
                              size: 16,
                            ),
                          ),
                        ),
                      ],
                      const SizedBox(width: 8),
                      Icon(
                        Icons.arrow_forward_ios,
                        color: Colors.orange.shade600,
                        size: 16,
                      ),
                    ],
                  ),

                  if (recentNotifications.isNotEmpty) ...[
                    const SizedBox(height: 12),
                    const Divider(height: 1),
                    const SizedBox(height: 12),

                    // أحدث الإشعارات مع تأثير انتقالي
                    ...recentNotifications.map((notification) {
                      return AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                        margin: const EdgeInsets.only(bottom: 8),
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: notification.isRead
                              ? Colors.white.withValues(alpha: 0.7)
                              : Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: notification.isRead
                                ? Colors.grey.shade200
                                : Colors.orange.shade300,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.withValues(alpha: 0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Row(
                          children: [
                            Text(
                              notification.type.icon,
                              style: TextStyle(
                                fontSize: 16,
                                color: _getNotificationTypeColor(
                                  notification.type,
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    notification.title,
                                    style: TextStyle(
                                      fontSize: 13,
                                      fontWeight: notification.isRead
                                          ? FontWeight.normal
                                          : FontWeight.bold,
                                      color: const Color(0xFF2C3E50),
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  if (notification.message.isNotEmpty)
                                    Text(
                                      notification.message,
                                      style: TextStyle(
                                        fontSize: 11,
                                        color: Colors.grey.shade600,
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                ],
                              ),
                            ),
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                if (!notification.isRead) ...[
                                  // زر تحديد كمقروء
                                  InkWell(
                                    onTap: () {
                                      notificationProvider.markAsRead(
                                        notification.id,
                                      );
                                      ScaffoldMessenger.of(
                                        context,
                                      ).showSnackBar(
                                        const SnackBar(
                                          content: Text(
                                            'تم إزالة الإشعار من الشاشة ✓',
                                          ),
                                          backgroundColor: Colors.green,
                                          duration: Duration(seconds: 1),
                                        ),
                                      );
                                    },
                                    borderRadius: BorderRadius.circular(6),
                                    child: Container(
                                      padding: const EdgeInsets.all(4),
                                      decoration: BoxDecoration(
                                        color: Colors.green.shade100,
                                        borderRadius: BorderRadius.circular(6),
                                      ),
                                      child: Icon(
                                        Icons.done,
                                        color: Colors.green.shade700,
                                        size: 14,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 6),
                                  Container(
                                    width: 8,
                                    height: 8,
                                    decoration: const BoxDecoration(
                                      color: Colors.red,
                                      shape: BoxShape.circle,
                                    ),
                                  ),
                                ] else ...[
                                  Icon(
                                    Icons.check_circle,
                                    color: Colors.green.shade600,
                                    size: 16,
                                  ),
                                ],
                              ],
                            ),
                          ],
                        ),
                      );
                    }),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // الحصول على لون نوع الإشعار
  Color _getNotificationTypeColor(NotificationType type) {
    switch (type) {
      case NotificationType.overdue:
        return Colors.red;
      case NotificationType.dueToday:
        return Colors.orange;
      case NotificationType.dueSoon:
        return Colors.blue;
      case NotificationType.lowStock:
        return Colors.purple;
      case NotificationType.outOfStock:
        return Colors.red.shade800;
      default:
        return Colors.grey;
    }
  }

  // بناء زر إضافة الدين البسيط
  Widget _buildAddDebtButton() {
    return InkWell(
      onTap: () => _showAddDebtBottomSheet(context, null),
      borderRadius: BorderRadius.circular(30),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 10),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: Colors.blue.shade600,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.blue.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: const Icon(Icons.add, color: Colors.white, size: 28),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDrawer() {
    return SizedBox(
      width: 300,
      child: Drawer(
        backgroundColor: Colors.white,
        child: Column(
          children: [
            // Header
            Container(
              height: 110,
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [Colors.blue.shade600, Colors.blue.shade800],
                ),
              ),
              child: SafeArea(
                child: Center(
                  child: Container(
                    padding: const EdgeInsets.all(14),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: const Icon(
                      Icons.account_balance_wallet,
                      color: Colors.white,
                      size: 36,
                    ),
                  ),
                ),
              ),
            ),

            // Menu Items
            Expanded(
              child: ListView(
                padding: const EdgeInsets.symmetric(vertical: 8),
                children: [
                  // تم إزالة زر تحديث التنبيهات - التحديث تلقائي الآن
                  _buildDrawerItem(
                    icon: Icons.notifications_rounded,
                    title: 'التنبيهات الذكية',
                    color: Colors.orange.shade600,
                    onTap: () {
                      Navigator.pop(context);
                      _navigateToPage(const SmartNotificationsScreen());
                    },
                    trailing: Consumer<SmartNotificationProvider>(
                      builder: (context, notificationProvider, _) {
                        if (notificationProvider.unreadCount > 0) {
                          return Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              '${notificationProvider.unreadCount}',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          );
                        }
                        return const SizedBox.shrink();
                      },
                    ),
                  ),
                  const Divider(height: 1),
                  _buildDrawerItem(
                    icon: Icons.receipt_long_outlined,
                    title: 'نظرة عامة للديون',
                    color: Colors.purple.shade600,
                    onTap: () {
                      Navigator.pop(context);
                      _navigateToPage(const DebtsOverviewScreen());
                    },
                  ),
                  _buildDrawerItem(
                    icon: Icons.inventory_2_rounded,
                    title: 'إدارة الكميات',
                    color: Colors.blue.shade600,
                    onTap: () {
                      Navigator.pop(context);
                      _navigateToPage(const CardInventoryScreen());
                    },
                  ),
                  _buildDrawerItem(
                    icon: Icons.trending_up_rounded,
                    title: 'تتبع الأرباح',
                    color: Colors.green.shade600,
                    onTap: () {
                      Navigator.pop(context);
                      _navigateToPage(const CardProfitScreen());
                    },
                  ),
                  _buildDrawerItem(
                    icon: Icons.cloud_upload_rounded,
                    title: 'النسخ الاحتياطي',
                    color: Colors.teal.shade600,
                    onTap: () {
                      Navigator.pop(context);
                      _showBackupDialog(context);
                    },
                  ),
                  const Divider(height: 1),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required Color color,
    required VoidCallback onTap,
    Widget? trailing,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 3),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.grey.shade50,
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(icon, color: color, size: 24),
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        trailing: trailing ??
            const Icon(Icons.chevron_left, color: Colors.grey, size: 20),
        onTap: onTap,
        contentPadding: const EdgeInsets.symmetric(horizontal: 14, vertical: 6),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  void _showAddDebtBottomSheet(BuildContext context, Customer? customer) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AddDebtBottomSheet(customer: customer),
    ).then((_) {
      // تحديث التنبيهات فوراً بعد إغلاق نافذة إضافة الدين
      if (mounted) {
        debugPrint('🔄 تحديث فوري للتنبيهات بعد إضافة دين...');
        _refreshNotifications();
      }
    });
  }

  void _showBackupDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(Icons.backup, color: Colors.green.shade600, size: 24),
            ),
            const SizedBox(width: 12),
            const Text('نسخ احتياطي'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildBackupOption(
              icon: Icons.cloud_upload,
              title: 'إنشاء نسخة احتياطية',
              subtitle: 'حفظ جميع البيانات في ذاكرة الهاتف',
              color: Colors.green,
              onTap: () {
                Navigator.pop(context);
                _createBackup(context);
              },
            ),
            const SizedBox(height: 12),
            _buildBackupOption(
              icon: Icons.cloud_download,
              title: 'استرجاع نسخة احتياطية',
              subtitle: 'استيراد البيانات من ملف محفوظ',
              color: Colors.blue,
              onTap: () {
                Navigator.pop(context);
                _restoreBackup(context);
              },
            ),
            const SizedBox(height: 12),
            _buildBackupOption(
              icon: Icons.folder,
              title: 'عرض النسخ المحفوظة',
              subtitle: 'إدارة الملفات المحفوظة',
              color: Colors.orange,
              onTap: () {
                Navigator.pop(context);
                _showSavedBackups(context);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  // إنشاء نسخة احتياطية
  Future<void> _createBackup(BuildContext context) async {
    try {
      // إظهار مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      final backupService = BackupService();
      final filePath = await backupService.createFullBackup();

      if (context.mounted) {
        Navigator.pop(context); // إغلاق مؤشر التحميل

        // مشاركة الملف
        await Share.shareXFiles([
          XFile(filePath),
        ], text: 'نسخة احتياطية من تطبيق محاسب الديون');

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('تم إنشاء النسخة الاحتياطية بنجاح'),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        Navigator.pop(context); // إغلاق مؤشر التحميل
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ غير متوقع: $e'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }

  // بناء شريط البحث الاحترافي المحسن
  Widget _buildProfessionalSearchBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // أيقونة البحث الأنيقة
          Container(
            padding: const EdgeInsets.all(14),
            child: Icon(
              Icons.search_rounded,
              color: Colors.grey.shade600,
              size: 22,
            ),
          ),

          // حقل البحث
          Expanded(
            child: TextField(
              controller: _searchController,
              focusNode: _searchFocusNode,
              textDirection: TextDirection.rtl,
              style: const TextStyle(
                color: Colors.black87,
                fontSize: 16,
                fontWeight: FontWeight.w500,
                height: 1.4,
              ),
              decoration: InputDecoration(
                hintText: 'ابحث عن عميل بالاسم أو رقم الهاتف',
                hintStyle: TextStyle(
                  color: Colors.grey.shade500,
                  fontSize: 15,
                  fontWeight: FontWeight.w400,
                ),
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 4,
                  vertical: 16,
                ),
              ),
              onChanged: _onSearchChanged,
            ),
          ),

          // فاصل رفيع
          Container(
            height: 24,
            width: 1,
            color: Colors.grey.shade300,
            margin: const EdgeInsets.symmetric(horizontal: 8),
          ),

          // زر التحديد المتعدد
          Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                _toggleSelectionMode();
              },
              borderRadius: BorderRadius.circular(10),
              child: Container(
                padding: const EdgeInsets.all(10),
                child: Icon(
                  Icons.checklist_rtl_rounded,
                  color: Colors.orange.shade600,
                  size: 20,
                ),
              ),
            ),
          ),

          // فاصل رفيع
          Container(
            height: 24,
            width: 1,
            color: Colors.grey.shade300,
            margin: const EdgeInsets.symmetric(horizontal: 4),
          ),

          // زر الإغلاق الأنيق
          Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: _toggleSearchMode,
              borderRadius: BorderRadius.circular(10),
              child: Container(
                padding: const EdgeInsets.all(10),
                margin: const EdgeInsets.only(right: 4),
                child: Icon(
                  Icons.close_rounded,
                  color: Colors.grey.shade600,
                  size: 20,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بناء خيار النسخ الاحتياطي
  Widget _buildBackupOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: color.withValues(alpha: 0.3)),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: Colors.grey.shade400,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  // استرجاع نسخة احتياطية
  Future<void> _restoreBackup(BuildContext context) async {
    try {
      final backupService = BackupService();
      final filePath = await backupService.pickBackupFile();

      if (filePath == null) return;

      // تأكيد الاستعادة
      final confirm = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('تأكيد الاستعادة'),
          content: const Text(
            'سيتم حذف جميع البيانات الحالية واستبدالها بالبيانات من النسخة الاحتياطية. هل تريد المتابعة؟',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.pop(context, true),
              child: const Text('استعادة'),
            ),
          ],
        ),
      );

      if (confirm != true) return;

      // عرض مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      // استعادة النسخة الاحتياطية
      await backupService.restoreFromBackup(filePath);

      // إغلاق مؤشر التحميل
      Navigator.pop(context);

      // عرض رسالة النجاح
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('تمت الاستعادة بنجاح'),
          content: const Text('تم استرجاع جميع البيانات من النسخة الاحتياطية'),
          actions: [
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                // إعادة تحميل الصفحة
                setState(() {});
              },
              child: const Text('موافق'),
            ),
          ],
        ),
      );
    } catch (e) {
      // إغلاق مؤشر التحميل إذا كان مفتوحاً
      Navigator.pop(context);

      // عرض رسالة الخطأ
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('خطأ'),
          content: Text('فشل في استرجاع النسخة الاحتياطية: $e'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('موافق'),
            ),
          ],
        ),
      );
    }
  }

  // عرض النسخ المحفوظة
  Future<void> _showSavedBackups(BuildContext context) async {
    final backupService = BackupService();
    final backupFiles = await backupService.getBackupFiles();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('النسخ الاحتياطية المحفوظة'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: backupFiles.isEmpty
              ? const Center(child: Text('لا توجد نسخ احتياطية محفوظة'))
              : ListView.builder(
                  itemCount: backupFiles.length,
                  itemBuilder: (context, index) {
                    final file = backupFiles[index];
                    final fileName = file.path.split('/').last;
                    final fileStats = file.statSync();

                    return ListTile(
                      leading: const Icon(Icons.backup),
                      title: Text(fileName),
                      subtitle: Text(
                        'تاريخ الإنشاء: ${fileStats.modified.toString().split('.')[0]}',
                      ),
                      trailing: PopupMenuButton(
                        itemBuilder: (context) => [
                          PopupMenuItem(
                            child: const Text('استعادة'),
                            onTap: () async {
                              Navigator.pop(context);
                              await _restoreFromFile(context, file.path);
                            },
                          ),
                          PopupMenuItem(
                            child: const Text('مشاركة'),
                            onTap: () async {
                              await backupService.shareBackupFile(file.path);
                            },
                          ),
                          PopupMenuItem(
                            child: const Text('حذف'),
                            onTap: () async {
                              await file.delete();
                              Navigator.pop(context);
                              _showSavedBackups(context);
                            },
                          ),
                        ],
                      ),
                    );
                  },
                ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  // استعادة من ملف محدد
  Future<void> _restoreFromFile(BuildContext context, String filePath) async {
    try {
      // تأكيد الاستعادة
      final confirm = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('تأكيد الاستعادة'),
          content: const Text(
            'سيتم حذف جميع البيانات الحالية واستبدالها بالبيانات من النسخة الاحتياطية. هل تريد المتابعة؟',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.pop(context, true),
              child: const Text('استعادة'),
            ),
          ],
        ),
      );

      if (confirm != true) return;

      // عرض مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      // استعادة النسخة الاحتياطية
      final backupService = BackupService();
      await backupService.restoreFromBackup(filePath);

      // إغلاق مؤشر التحميل
      Navigator.pop(context);

      // عرض رسالة النجاح
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('تمت الاستعادة بنجاح'),
          content: const Text('تم استرجاع جميع البيانات من النسخة الاحتياطية'),
          actions: [
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                // إعادة تحميل الصفحة
                setState(() {});
              },
              child: const Text('موافق'),
            ),
          ],
        ),
      );
    } catch (e) {
      // إغلاق مؤشر التحميل إذا كان مفتوحاً
      Navigator.pop(context);

      // عرض رسالة الخطأ
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('خطأ'),
          content: Text('فشل في استرجاع النسخة الاحتياطية: $e'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('موافق'),
            ),
          ],
        ),
      );
    }
  }
}

// NavigatorObserver مخصص لمراقبة التنقل
class _HomeNavigatorObserver extends NavigatorObserver {
  _HomeNavigatorObserver({required this.onRoutePopped});
  final VoidCallback onRoutePopped;

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPop(route, previousRoute);
    // عند العودة إلى الشاشة الرئيسية
    if (previousRoute?.settings.name == '/') {
      onRoutePopped();
    }
  }
}

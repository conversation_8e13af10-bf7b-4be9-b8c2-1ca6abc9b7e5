import 'package:flutter/foundation.dart';
import '../models/notification_model.dart';
import '../models/debt.dart';
import '../models/custom_card_type.dart';
import '../providers/debt_provider.dart';
import '../providers/customer_provider.dart';
import '../providers/card_inventory_provider.dart';
import '../providers/notification_provider.dart';
import '../providers/card_type_provider.dart';
import '../services/local_notification_service.dart';
import '../utils/number_formatter.dart';

class SmartNotificationProvider with ChangeNotifier {
  final List<NotificationModel> _notifications = [];
  bool _isLoading = false;

  List<NotificationModel> get notifications => _notifications;
  bool get isLoading => _isLoading;

  // الحصول على التنبيهات غير المقروءة
  List<NotificationModel> get unreadNotifications =>
      _notifications.where((n) => !n.isRead).toList();

  // عدد التنبيهات غير المقروءة
  int get unreadCount => unreadNotifications.length;

  // الحصول على التنبيهات حسب النوع
  List<NotificationModel> getNotificationsByType(NotificationType type) {
    return _notifications.where((n) => n.type == type).toList();
  }

  // الحصول على التنبيهات حسب العميل
  List<NotificationModel> getNotificationsByCustomer(String customerId) {
    return _notifications.where((n) => n.customerId == customerId).toList();
  }

  // إنشاء التنبيهات الذكية
  Future<void> generateSmartNotifications({
    required DebtProvider debtProvider,
    required CustomerProvider customerProvider,
    required CardInventoryProvider inventoryProvider,
    NotificationProvider? notificationProvider,
  }) async {
    debugPrint('🚀 بدء إنشاء الإشعارات الذكية...');
    debugPrint('📊 عدد الديون: ${debtProvider.debts.length}');
    debugPrint('👥 عدد العملاء: ${customerProvider.customers.length}');

    _isLoading = true;
    notifyListeners();

    try {
      // عدم مسح التنبيهات القديمة - البطاقات تبقى ظاهرة دائماً
      final oldCount = _notifications.length;
      // _notifications.clear();

      // عدم إزالة إشعارات الملخص
      // final localNotificationService = LocalNotificationService();
      // await localNotificationService.cancelSummaryNotification();

      debugPrint(
          '📋 الاحتفاظ بـ $oldCount إشعار موجود - البطاقات تبقى ظاهرة دائماً');

      // إنشاء تنبيهات الديون
      await _generateDebtNotifications(
        debtProvider,
        customerProvider,
        notificationProvider,
      );

      // إنشاء تنبيهات المخزون
      await _generateInventoryNotifications(inventoryProvider);

      // ترتيب التنبيهات حسب الأولوية والتاريخ
      _sortNotifications();

      debugPrint('✅ تم إنشاء ${_notifications.length} إشعار جديد');
      debugPrint('🔔 غير مقروء: $unreadCount');
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء التنبيهات: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // إنشاء تنبيهات الديون
  Future<void> _generateDebtNotifications(
    DebtProvider debtProvider,
    CustomerProvider customerProvider,
    NotificationProvider? notificationProvider,
  ) async {
    debugPrint('💳 بدء إنشاء إشعارات الديون...');
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    // تجميع الديون حسب العميل ونوع التنبيه
    final Map<String, List<Debt>> overdueByCustomer = {};
    final Map<String, List<Debt>> dueTodayByCustomer = {};
    final Map<String, List<Debt>> dueSoonByCustomer = {};

    int processedDebts = 0;
    for (final debt in debtProvider.debts) {
      // إزالة فلترة الديون المدفوعة - عرض جميع الديون
      // if (debt.status == DebtStatus.paid) continue;

      processedDebts++;

      final customer = customerProvider.customers.firstWhere(
        (c) => c.id == debt.customerId,
        orElse: () {
          debugPrint('⚠️ لم يتم العثور على العميل: ${debt.customerId}');
          // تخطي هذا الدين إذا لم يتم العثور على العميل
          return customerProvider
              .customers.first; // استخدام أول عميل كبديل مؤقت
        },
      );

      // تخطي الدين إذا لم يتم العثور على العميل الصحيح
      if (customer.id != debt.customerId) {
        debugPrint('⚠️ تم تخطي دين للعميل غير الموجود: ${debt.customerId}');
        continue;
      }

      final dueDate = DateTime(
        debt.dueDate.year,
        debt.dueDate.month,
        debt.dueDate.day,
      );

      final daysDifference = dueDate.difference(today).inDays;
      final customerKey = '${customer.id}_${customer.name}';

      // تصنيف الديون حسب حالة الاستحقاق
      if (daysDifference < 0) {
        overdueByCustomer.putIfAbsent(customerKey, () => []).add(debt);
        debugPrint('🔴 دين متأخر: ${customer.name} - $daysDifference يوم');
      } else if (daysDifference == 0) {
        dueTodayByCustomer.putIfAbsent(customerKey, () => []).add(debt);
        debugPrint('🟠 دين مستحق اليوم: ${customer.name}');
      } else if (daysDifference <= 3) {
        dueSoonByCustomer.putIfAbsent(customerKey, () => []).add(debt);
        debugPrint(
          '🔵 دين مستحق قريباً: ${customer.name} - $daysDifference يوم',
        );
      }
    }

    debugPrint('📊 تم معالجة $processedDebts دين');
    debugPrint('🔴 عملاء متأخرين: ${overdueByCustomer.length}');
    debugPrint('🟠 عملاء مستحق اليوم: ${dueTodayByCustomer.length}');
    debugPrint('🔵 عملاء مستحق قريباً: ${dueSoonByCustomer.length}');

    // إنشاء تنبيهات مجمعة للديون المتأخرة
    for (final entry in overdueByCustomer.entries) {
      final customerKey = entry.key;
      final debts = entry.value;
      final customer = customerProvider.customers.firstWhere(
        (c) => '${c.id}_${c.name}' == customerKey,
      );

      debugPrint('🔴 إنشاء إشعار دين متأخر للعميل: ${customer.name}');
      await _createGroupedNotification(
        customer,
        debts,
        NotificationType.overdue,
        NotificationPriority.urgent,
        'ديون متأخرة',
        now,
        notificationProvider,
      );
    }

    // إنشاء تنبيهات مجمعة للديون المستحقة اليوم
    for (final entry in dueTodayByCustomer.entries) {
      final customerKey = entry.key;
      final debts = entry.value;
      final customer = customerProvider.customers.firstWhere(
        (c) => '${c.id}_${c.name}' == customerKey,
      );

      debugPrint('🟠 إنشاء إشعار دين مستحق اليوم للعميل: ${customer.name}');
      await _createGroupedNotification(
        customer,
        debts,
        NotificationType.dueToday,
        NotificationPriority.high,
        'ديون مستحقة اليوم',
        now,
        notificationProvider,
      );
    }

    // إنشاء تنبيهات مجمعة للديون المستحقة قريباً
    for (final entry in dueSoonByCustomer.entries) {
      final customerKey = entry.key;
      final debts = entry.value;
      final customer = customerProvider.customers.firstWhere(
        (c) => '${c.id}_${c.name}' == customerKey,
      );

      await _createGroupedNotification(
        customer,
        debts,
        NotificationType.dueSoon,
        NotificationPriority.medium,
        'ديون مستحقة قريباً',
        now,
        notificationProvider,
      );
    }
  }

  // إنشاء تنبيه مجمع للعميل
  Future<void> _createGroupedNotification(
    dynamic customer,
    List<Debt> debts,
    NotificationType type,
    NotificationPriority priority,
    String titlePrefix,
    DateTime now,
    NotificationProvider? notificationProvider,
  ) async {
    debugPrint('📝 بدء إنشاء إشعار مجمع للعميل: ${customer.name}');
    debugPrint('   - نوع الإشعار: ${type.name}');
    debugPrint('   - عدد الديون: ${debts.length}');

    final totalAmount = debts.fold<double>(
      0,
      (sum, debt) => sum + debt.remainingAmount,
    );
    final debtCount = debts.length;

    debugPrint('   - إجمالي المبلغ: $totalAmount');

    // حساب أقصى تأخير للديون المتأخرة
    int maxDaysOverdue = 0;
    if (type == NotificationType.overdue) {
      final today = DateTime(now.year, now.month, now.day);
      for (final debt in debts) {
        final dueDate = DateTime(
          debt.dueDate.year,
          debt.dueDate.month,
          debt.dueDate.day,
        );
        final daysDiff = today.difference(dueDate).inDays;
        if (daysDiff > maxDaysOverdue) {
          maxDaysOverdue = daysDiff;
        }
      }
    }

    // إنشاء رسالة التنبيه
    String message;
    if (debtCount == 1) {
      final debt = debts.first;
      if (type == NotificationType.overdue) {
        final today = DateTime(now.year, now.month, now.day);
        final dueDate = DateTime(
          debt.dueDate.year,
          debt.dueDate.month,
          debt.dueDate.day,
        );
        final daysDiff = today.difference(dueDate).inDays;
        message =
            'دين ${customer.name} متأخر $daysDiff يوم - ${NumberFormatter.formatCurrency(totalAmount)}';
      } else if (type == NotificationType.dueToday) {
        message =
            'دين ${customer.name} مستحق اليوم - ${NumberFormatter.formatCurrency(totalAmount)}';
      } else {
        final debt = debts.first;
        final today = DateTime(now.year, now.month, now.day);
        final dueDate = DateTime(
          debt.dueDate.year,
          debt.dueDate.month,
          debt.dueDate.day,
        );
        final daysDiff = dueDate.difference(today).inDays;
        message =
            'دين ${customer.name} مستحق خلال $daysDiff أيام - ${NumberFormatter.formatCurrency(totalAmount)}';
      }
    } else {
      if (type == NotificationType.overdue) {
        message =
            '${customer.name} لديه $debtCount ديون متأخرة (أقصى تأخير: $maxDaysOverdue يوم) - إجمالي: ${NumberFormatter.formatCurrency(totalAmount)}';
      } else if (type == NotificationType.dueToday) {
        message =
            '${customer.name} لديه $debtCount ديون مستحقة اليوم - إجمالي: ${NumberFormatter.formatCurrency(totalAmount)}';
      } else {
        message =
            '${customer.name} لديه $debtCount ديون مستحقة قريباً - إجمالي: ${NumberFormatter.formatCurrency(totalAmount)}';
      }
    }

    // جمع معلومات إضافية عن جميع الديون
    final List<Map<String, dynamic>> debtsInfo = debts.map((debt) {
      final today = DateTime(now.year, now.month, now.day);
      final dueDate = DateTime(
        debt.dueDate.year,
        debt.dueDate.month,
        debt.dueDate.day,
      );
      final daysDifference = dueDate.difference(today).inDays;

      return {
        'debtId': debt.id?.toString(),
        'amount': debt.remainingAmount,
        'cardType': debt.cardType,
        'notes': debt.notes ?? '',
        'createdAt': debt.entryDate.toIso8601String(),
        'dueDate': debt.dueDate.toIso8601String(),
        'daysDifference': daysDifference,
      };
    }).toList();

    // إنشاء عنوان مفصل للإشعار
    String detailedTitle;
    if (debtCount == 1) {
      detailedTitle = 'العميل ${customer.name} لديه دين $titlePrefix';
    } else {
      detailedTitle =
          'العميل ${customer.name} لديه $debtCount ديون ${titlePrefix.toLowerCase()}';
    }

    _addNotification(
      NotificationModel(
        id: '${type.name}_customer_${customer.id}',
        title: detailedTitle,
        message: message,
        type: type,
        priority: priority,
        createdAt: now,
        customerId: customer.id?.toString(),
        customerName: customer.name,
        amount: totalAmount,
        dueDate: debts.first.dueDate, // استخدام تاريخ استحقاق أول دين
        additionalData: {
          'customerName': customer.name,
          'phoneNumber': customer.phoneNumber,
          'totalAmount': totalAmount,
          'debtCount': debtCount,
          'debts': debtsInfo,
          'maxDaysOverdue': maxDaysOverdue,
          'cardTypes': debts.map((d) => d.cardType).toSet().toList(),
        },
      ),
    );

    // إرسال إشعار محلي إذا كانت الإعدادات تسمح بذلك
    if (notificationProvider != null &&
        notificationProvider.settings.localNotificationsEnabled &&
        notificationProvider.settings.debtNotificationsEnabled) {
      final localNotificationService = LocalNotificationService();

      switch (type) {
        case NotificationType.overdue:
          if (notificationProvider.settings.overdueNotificationsEnabled) {
            // تحديث رقم هاتف العميل في LocalNotificationService
            if (customer.phone != null && customer.phone!.isNotEmpty) {
              localNotificationService.addCustomerPhone(
                customer.id?.toString() ?? '',
                customer.phone!,
              );
            }

            await localNotificationService.showOverdueDebtNotification(
              customerName: customer.name,
              amount: totalAmount,
              debtCount: debtCount,
              customerId: customer.id?.toString(),
            );
          }
          break;

        case NotificationType.dueToday:
          if (notificationProvider.settings.dueTodayNotificationsEnabled) {
            // تحديث رقم هاتف العميل في LocalNotificationService
            if (customer.phone != null && customer.phone!.isNotEmpty) {
              localNotificationService.addCustomerPhone(
                customer.id?.toString() ?? '',
                customer.phone!,
              );
            }

            await localNotificationService.showDueTodayNotification(
              customerName: customer.name,
              amount: totalAmount,
              debtCount: debtCount,
              customerId: customer.id?.toString(),
            );
          }
          break;

        case NotificationType.dueSoon:
          if (notificationProvider.settings.dueSoonNotificationsEnabled) {
            // حساب أقل عدد أيام للاستحقاق
            int minDaysUntilDue = 999;
            final today = DateTime(now.year, now.month, now.day);
            for (final debt in debts) {
              final dueDate = DateTime(
                debt.dueDate.year,
                debt.dueDate.month,
                debt.dueDate.day,
              );
              final daysDiff = dueDate.difference(today).inDays;
              if (daysDiff < minDaysUntilDue) {
                minDaysUntilDue = daysDiff;
              }
            }

            await localNotificationService.showDueSoonNotification(
              customerName: customer.name,
              amount: totalAmount,
              debtCount: debtCount,
              daysUntilDue: minDaysUntilDue,
              customerId: customer.id?.toString(),
            );
          }
          break;

        default:
          break;
      }
    }
  }

  // إنشاء تنبيهات المخزون
  Future<void> _generateInventoryNotifications(
    CardInventoryProvider inventoryProvider,
  ) async {
    final now = DateTime.now();

    for (final inventory in inventoryProvider.inventories) {
      // تنبيهات نفاد المخزون
      if (inventory.isOutOfStock) {
        _addNotification(
          NotificationModel(
            id: 'out_of_stock_${inventory.id}',
            title: 'نفاد المخزون',
            message:
                'نفد مخزون ${inventory.cardType} - الكمية: ${inventory.quantity}',
            type: NotificationType.outOfStock,
            priority: NotificationPriority.urgent,
            createdAt: now,
            additionalData: {
              'cardType': inventory.cardType,
              'quantity': inventory.quantity,
            },
          ),
        );
      }
      // تنبيهات المخزون المنخفض
      else if (inventory.isLowStock) {
        _addNotification(
          NotificationModel(
            id: 'low_stock_${inventory.id}',
            title: 'مخزون منخفض',
            message:
                'مخزون ${inventory.cardType} منخفض - الكمية: ${inventory.quantity}',
            type: NotificationType.lowStock,
            priority: NotificationPriority.high,
            createdAt: now,
            additionalData: {
              'cardType': inventory.cardType,
              'quantity': inventory.quantity,
              'minQuantity': inventory.minQuantity,
            },
          ),
        );
      }
    }
  }

  // إضافة تنبيه
  void _addNotification(NotificationModel notification) {
    // تجنب التكرار
    if (!_notifications.any((n) => n.id == notification.id)) {
      _notifications.add(notification);
      debugPrint('✅ تم إضافة إشعار جديد: ${notification.title}');
      debugPrint('📊 إجمالي الإشعارات: ${_notifications.length}');
      debugPrint('🔔 غير مقروء: $unreadCount');
      notifyListeners(); // إشعار المستمعين بالتحديث

      // لا نحتاج لإشعار الملخص - فقط إشعارات الديون الفردية
    } else {
      debugPrint('⚠️ إشعار مكرر تم تجاهله: ${notification.id}');
    }
  }

  // إضافة تنبيه من خدمة خارجية
  void addExternalNotification(NotificationModel notification) {
    _addNotification(notification);
  }

  // ترتيب التنبيهات
  void _sortNotifications() {
    _notifications.sort((a, b) {
      // ترتيب حسب الأولوية أولاً
      final priorityComparison = _getPriorityValue(
        b.priority,
      ).compareTo(_getPriorityValue(a.priority));

      if (priorityComparison != 0) return priorityComparison;

      // ثم حسب التاريخ (الأحدث أولاً)
      return b.createdAt.compareTo(a.createdAt);
    });
  }

  // الحصول على قيمة الأولوية للترتيب
  int _getPriorityValue(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.urgent:
        return 4;
      case NotificationPriority.high:
        return 3;
      case NotificationPriority.medium:
        return 2;
      case NotificationPriority.low:
        return 1;
    }
  }

  // تحديد التنبيه كمقروء بدون إزالته - البطاقات تبقى ظاهرة دائماً
  void markAsRead(String notificationId) {
    final index = _notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      // تحديد الإشعار كمقروء فقط بدون إزالته
      _notifications[index] = _notifications[index].copyWith(isRead: true);
      notifyListeners();

      // إزالة الإشعار المحلي المقابل إذا وجد
      _removeLocalNotificationIfExists(notificationId);

      debugPrint(
          '✅ تم تحديد الإشعار $notificationId كمقروء - البطاقة تبقى ظاهرة');
    }
  }

  // تحديد جميع التنبيهات كمقروءة بدون إزالتها - البطاقات تبقى ظاهرة دائماً
  void markAllAsRead() {
    // تحديد جميع الإشعارات كمقروءة فقط بدون إزالتها
    final updatedCount = _notifications.length;
    for (int i = 0; i < _notifications.length; i++) {
      _notifications[i] = _notifications[i].copyWith(isRead: true);
    }
    notifyListeners();

    // إزالة جميع الإشعارات المحلية
    _removeAllLocalNotifications();

    debugPrint(
        '✅ تم تحديد جميع الإشعارات ($updatedCount) كمقروءة - البطاقات تبقى ظاهرة');
  }

  // إزالة الإشعار المحلي إذا وجد
  void _removeLocalNotificationIfExists(String notificationId) {
    try {
      // محاولة تحويل معرف الإشعار إلى رقم
      // معرف الإشعار قد يكون في شكل: "notification_123" أو "123"
      int? localNotificationId;

      if (notificationId.contains('_')) {
        final parts = notificationId.split('_');
        if (parts.length > 1) {
          localNotificationId = int.tryParse(parts.last);
        }
      } else {
        localNotificationId = int.tryParse(notificationId);
      }

      if (localNotificationId != null) {
        LocalNotificationService().cancelNotification(localNotificationId);
        debugPrint('✅ تم إزالة الإشعار المحلي: $localNotificationId');
      }
    } catch (e) {
      debugPrint('خطأ في إزالة الإشعار المحلي: $e');
    }
  }

  // إزالة جميع الإشعارات المحلية
  void _removeAllLocalNotifications() {
    try {
      LocalNotificationService().cancelAllNotifications();
      debugPrint('✅ تم إزالة جميع الإشعارات المحلية');
    } catch (e) {
      debugPrint('خطأ في إزالة الإشعارات المحلية: $e');
    }
  }

  // تحديث إشعار الملخص في إشعارات الموبايل
  Future<void> updateNotificationSummary() async {
    try {
      final localNotificationService = LocalNotificationService();

      if (_notifications.isEmpty) {
        // إزالة إشعار الملخص إذا لم تعد هناك إشعارات
        await localNotificationService.cancelSummaryNotification();
        return;
      }

      final totalCount = _notifications.length;
      final unreadCount = this.unreadCount;
      final recentTitles = _notifications
          .take(5)
          .map((n) => '${n.type.icon} ${n.title}')
          .toList();

      // تجميع البيانات حسب نوع الإشعار
      final overdueNotifications = _notifications
          .where((n) => n.type == NotificationType.overdue)
          .toList();
      final dueTodayNotifications = _notifications
          .where((n) => n.type == NotificationType.dueToday)
          .toList();

      // استخراج تفاصيل العملاء والمبالغ وأرقام الهواتف
      final overdueCustomers = <Map<String, dynamic>>[];
      final dueTodayCustomers = <Map<String, dynamic>>[];
      double totalOverdueAmount = 0;
      double totalDueTodayAmount = 0;

      for (final notification in overdueNotifications) {
        // استخراج تفاصيل العميل من البيانات الإضافية
        final customerData = _extractCustomerDetails(notification);
        if (customerData['name'].isNotEmpty) {
          overdueCustomers.add(customerData);
          totalOverdueAmount += customerData['amount'];
        }
      }

      for (final notification in dueTodayNotifications) {
        final customerData = _extractCustomerDetails(notification);
        if (customerData['name'].isNotEmpty) {
          dueTodayCustomers.add(customerData);
          totalDueTodayAmount += customerData['amount'];
        }
      }

      // عرض أو تحديث إشعار الملخص
      await localNotificationService.showNotificationSummary(
        totalCount: totalCount,
        unreadCount: unreadCount,
        recentTitles: recentTitles,
        overdueCustomers: overdueCustomers,
        dueTodayCustomers: dueTodayCustomers,
        totalOverdueAmount: totalOverdueAmount,
        totalDueTodayAmount: totalDueTodayAmount,
      );

      debugPrint(
        '✅ تم تحديث إشعار الملخص: $totalCount إجمالي، $unreadCount غير مقروء',
      );
    } catch (e) {
      debugPrint('خطأ في تحديث إشعار الملخص: $e');
    }
  }

  // استخراج تفاصيل العميل الكاملة من الإشعار
  Map<String, dynamic> _extractCustomerDetails(NotificationModel notification) {
    final additionalData = notification.additionalData ?? {};

    // استخراج اسم العميل من العنوان أو البيانات الإضافية
    String customerName = '';
    if (additionalData.containsKey('customerName')) {
      customerName = additionalData['customerName'].toString();
    } else {
      // البحث عن اسم العميل في العنوان
      final regex = RegExp(r'العميل (.+?) لديه');
      final match = regex.firstMatch(notification.title);
      customerName = match?.group(1) ?? '';
    }

    // استخراج رقم الهاتف
    final phoneNumber = additionalData['phoneNumber']?.toString() ??
        additionalData['phone']?.toString() ??
        '';

    // استخراج المبلغ
    final amount = _extractAmount(additionalData);

    // استخراج عدد الديون
    final debtCount = additionalData['debtCount'] ?? 1;

    // استخراج تاريخ الاستحقاق
    final dueDate = additionalData['dueDate']?.toString() ?? '';

    // استخراج نوع البطاقة أو تفاصيل الدين
    final cardTypes = additionalData['cardTypes'] as List<dynamic>? ?? [];
    final cardTypesText = cardTypes.isNotEmpty
        ? cardTypes.take(2).join('، ') + (cardTypes.length > 2 ? '...' : '')
        : '';

    return {
      'name': customerName,
      'phone': phoneNumber,
      'amount': amount,
      'debtCount': debtCount,
      'dueDate': dueDate,
      'cardTypes': cardTypesText,
      'formattedAmount': _formatAmountForNotification(amount),
    };
  }

  // استخراج المبلغ من البيانات الإضافية
  double _extractAmount(Map<String, dynamic>? additionalData) {
    if (additionalData == null) return 0;

    // البحث عن المبلغ في البيانات الإضافية
    final amount = additionalData['totalAmount'] ??
        additionalData['amount'] ??
        additionalData['remainingAmount'] ??
        0;

    return (amount is num) ? amount.toDouble() : 0;
  }

  // تنسيق المبلغ للإشعارات
  String _formatAmountForNotification(double amount) {
    if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)} ألف';
    } else {
      return '${amount.toStringAsFixed(0)} ر.س';
    }
  }

  // حذف تنبيه
  void removeNotification(String notificationId) {
    _notifications.removeWhere((n) => n.id == notificationId);
    notifyListeners();
  }

  // إزالة التنبيهات المرتبطة بدين معين
  void removeNotificationsByDebt(int debtId) {
    final removedCount = _notifications.length;
    _notifications.removeWhere((n) =>
        n.additionalData?['debtId'] == debtId ||
        n.id.contains('debt_$debtId') ||
        n.id.contains('overdue_$debtId') ||
        n.id.contains('due_today_$debtId') ||
        n.id.contains('due_soon_$debtId'));
    final newCount = _notifications.length;
    if (removedCount != newCount) {
      debugPrint('🗑️ تم إزالة ${removedCount - newCount} تنبيه للدين $debtId');
      notifyListeners();
    }
  }

  // إضافة تنبيه لدين معين
  void addDebtNotification(Debt debt) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final dueDate =
        DateTime(debt.dueDate.year, debt.dueDate.month, debt.dueDate.day);

    NotificationType type;
    NotificationPriority priority;
    String title;
    String message;

    if (dueDate.isBefore(today)) {
      type = NotificationType.overdue;
      priority = NotificationPriority.urgent;
      title = 'دين متأخر';
      message =
          'دين ${debt.itemName} متأخر منذ ${today.difference(dueDate).inDays} يوم';
    } else if (dueDate.isAtSameMomentAs(today)) {
      type = NotificationType.dueToday;
      priority = NotificationPriority.high;
      title = 'دين مستحق اليوم';
      message = 'دين ${debt.itemName} مستحق اليوم';
    } else {
      type = NotificationType.dueSoon;
      priority = NotificationPriority.medium;
      title = 'دين مستحق قريباً';
      message =
          'دين ${debt.itemName} مستحق خلال ${dueDate.difference(today).inDays} يوم';
    }

    final notification = NotificationModel(
      id: '${type.name}_${debt.id}_${now.millisecondsSinceEpoch}',
      title: title,
      message: message,
      type: type,
      priority: priority,
      createdAt: now,
      additionalData: {
        'debtId': debt.id,
        'customerId': debt.customerId,
        'amount': debt.remainingAmount,
        'cardType': debt.cardType,
        'itemName': debt.itemName,
      },
    );

    _addNotification(notification);
  }

  // مسح جميع التنبيهات
  void clearAllNotifications() {
    _notifications.clear();
    notifyListeners();
  }

  // تشغيل الإشعارات التلقائية (يتم استدعاؤها دورياً)
  Future<void> triggerAutomaticNotifications({
    required DebtProvider debtProvider,
    required CustomerProvider customerProvider,
    required CardTypeProvider cardTypeProvider,
    NotificationProvider? notificationProvider,
  }) async {
    // إزالة قيود الإعدادات - التنبيهات تعمل دائماً
    // if (notificationProvider == null ||
    //     !notificationProvider.settings.localNotificationsEnabled ||
    //     !notificationProvider.settings.debtNotificationsEnabled) {
    //   return;
    // }

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    // البحث عن الديون المتأخرة
    final overdueDebts = debtProvider.debts.where((debt) {
      final dueDate = DateTime(
        debt.dueDate.year,
        debt.dueDate.month,
        debt.dueDate.day,
      );
      return dueDate.isBefore(today); // && debt.remainingAmount > 0;
    }).toList();

    // البحث عن الديون المستحقة اليوم
    final dueTodayDebts = debtProvider.debts.where((debt) {
      final dueDate = DateTime(
        debt.dueDate.year,
        debt.dueDate.month,
        debt.dueDate.day,
      );
      return dueDate.isAtSameMomentAs(today); // && debt.remainingAmount > 0;
    }).toList();

    // البحث عن الديون المستحقة قريباً
    final dueSoonDays = notificationProvider?.settings.dueSoonDays ?? 3;
    final dueSoonDate = today.add(Duration(days: dueSoonDays));
    final dueSoonDebts = debtProvider.debts.where((debt) {
      final dueDate = DateTime(
        debt.dueDate.year,
        debt.dueDate.month,
        debt.dueDate.day,
      );
      return dueDate.isAfter(today) &&
          dueDate.isBefore(dueSoonDate.add(const Duration(days: 1)));
      // && debt.remainingAmount > 0;
    }).toList();

    final localNotificationService = LocalNotificationService();

    // إرسال إشعارات الديون المتأخرة - إشعار منفصل لكل دين
    if (overdueDebts.isNotEmpty) {
      // && notificationProvider?.settings.overdueNotificationsEnabled == true) {
      for (final debt in overdueDebts) {
        final customer = customerProvider.customers.firstWhere(
          (c) => c.id == debt.customerId,
          orElse: () => throw Exception('Customer not found'),
        );

        // تحديث رقم هاتف العميل في LocalNotificationService
        if (customer.phone != null && customer.phone!.isNotEmpty) {
          localNotificationService.addCustomerPhone(
            customer.id?.toString() ?? '',
            customer.phone!,
          );
        }

        // تحديث معلومات البطاقة الحقيقية مع الاسم الحقيقي للكارت
        final cardInfo = {
          'cardType': _getRealCardTypeName(debt.cardType, cardTypeProvider),
          'quantity': debt.quantity.toString(),
          'notes': debt.notes ?? 'دين متأخر',
          'entryDate': _formatDateWithDayAndTime(debt.entryDate),
          'dueDate': _formatDateWithDay(debt.dueDate),
        };
        localNotificationService.addCardInfo(
          customer.id?.toString() ?? '',
          cardInfo,
        );

        // إرسال إشعار منفصل لكل دين متأخر
        await localNotificationService.showOverdueDebtNotification(
          customerName: customer.name,
          amount: debt.remainingAmount,
          debtCount: 1, // دين واحد فقط
          customerId: customer.id?.toString(),
        );

        debugPrint(
          '📱 تم إرسال إشعار دين متأخر منفصل: ${customer.name} - ${debt.remainingAmount}',
        );
      }
    }

    // إرسال إشعارات الديون المستحقة اليوم - إشعار منفصل لكل دين
    if (dueTodayDebts.isNotEmpty) {
      // && notificationProvider?.settings.dueTodayNotificationsEnabled == true) {
      for (final debt in dueTodayDebts) {
        final customer = customerProvider.customers.firstWhere(
          (c) => c.id == debt.customerId,
          orElse: () => throw Exception('Customer not found'),
        );

        // تحديث رقم هاتف العميل في LocalNotificationService
        if (customer.phone != null && customer.phone!.isNotEmpty) {
          localNotificationService.addCustomerPhone(
            customer.id?.toString() ?? '',
            customer.phone!,
          );
        }

        // تحديث معلومات البطاقة الحقيقية مع الاسم الحقيقي للكارت
        final cardInfo = {
          'cardType': _getRealCardTypeName(debt.cardType, cardTypeProvider),
          'quantity': debt.quantity.toString(),
          'notes': debt.notes ?? 'دين مستحق اليوم',
          'entryDate': _formatDateWithDayAndTime(debt.entryDate),
          'dueDate': _formatDateWithDay(debt.dueDate),
        };
        localNotificationService.addCardInfo(
          customer.id?.toString() ?? '',
          cardInfo,
        );

        // إرسال إشعار منفصل لكل دين مستحق اليوم
        await localNotificationService.showDueTodayNotification(
          customerName: customer.name,
          amount: debt.remainingAmount,
          debtCount: 1, // دين واحد فقط
          customerId: customer.id?.toString(),
        );

        debugPrint(
          '📱 تم إرسال إشعار دين مستحق اليوم منفصل: ${customer.name} - ${debt.remainingAmount}',
        );
      }
    }

    // إرسال إشعارات الديون المستحقة قريباً - إشعار منفصل لكل دين
    if (dueSoonDebts.isNotEmpty) {
      // && notificationProvider?.settings.dueSoonNotificationsEnabled == true) {
      for (final debt in dueSoonDebts) {
        final customer = customerProvider.customers.firstWhere(
          (c) => c.id == debt.customerId,
          orElse: () => throw Exception('Customer not found'),
        );

        // تحديث رقم هاتف العميل في LocalNotificationService
        if (customer.phone != null && customer.phone!.isNotEmpty) {
          localNotificationService.addCustomerPhone(
            customer.id?.toString() ?? '',
            customer.phone!,
          );
        }

        // تحديث معلومات البطاقة الحقيقية مع الاسم الحقيقي للكارت
        final cardInfo = {
          'cardType': _getRealCardTypeName(debt.cardType, cardTypeProvider),
          'quantity': debt.quantity.toString(),
          'notes': debt.notes ?? 'دين مستحق قريباً',
          'entryDate': _formatDateWithDayAndTime(debt.entryDate),
          'dueDate': _formatDateWithDay(debt.dueDate),
        };
        localNotificationService.addCardInfo(
          customer.id?.toString() ?? '',
          cardInfo,
        );

        // حساب عدد الأيام حتى الاستحقاق
        final dueDate = DateTime(
          debt.dueDate.year,
          debt.dueDate.month,
          debt.dueDate.day,
        );
        final daysUntilDue = dueDate.difference(today).inDays;

        // إرسال إشعار منفصل لكل دين مستحق قريباً
        await localNotificationService.showDueSoonNotification(
          customerName: customer.name,
          amount: debt.remainingAmount,
          debtCount: 1, // دين واحد فقط
          daysUntilDue: daysUntilDue,
          customerId: customer.id?.toString(),
        );

        debugPrint(
          '📱 تم إرسال إشعار دين مستحق قريباً منفصل: ${customer.name} - ${debt.remainingAmount} - $daysUntilDue أيام',
        );
      }
    }

    debugPrint('✅ تم تشغيل الإشعارات التلقائية');
  }

  // الحصول على الاسم الحقيقي للكارت
  String _getRealCardTypeName(
    String? cardType,
    CardTypeProvider cardTypeProvider,
  ) {
    if (cardType == null || cardType.isEmpty) return 'غير محدد';

    debugPrint('🔍 معالجة نوع الكارت: $cardType');

    // إذا كان نوع مخصص، احصل على الاسم الفعلي
    if (cardType.startsWith('custom_')) {
      try {
        final cardTypeId = int.parse(cardType.replaceFirst('custom_', ''));
        final customCardType = cardTypeProvider.customCardTypes.firstWhere(
          (ct) => ct.id == cardTypeId,
          orElse: () => throw Exception('Card type not found'),
        );
        debugPrint('✅ تم العثور على كارت مخصص: ${customCardType.displayName}');
        return customCardType.displayName; // الاسم الحقيقي المخصص
      } catch (e) {
        debugPrint('❌ خطأ في الحصول على نوع الكارت المخصص: $e');
        return 'كارت مخصص';
      }
    } else {
      // ترجمة الأسماء الإنجليزية إلى العربية
      final Map<String, String> translations = {
        'cash': 'نقدي',
        'visa': 'فيزا',
        'mastercard': 'ماستركارد',
        'mada': 'مدى',
        'americanExpress': 'أمريكان إكسبريس',
        'american_express': 'أمريكان إكسبريس',
        'zain': 'زين',
        'sia': 'آسيا',
        'abuAshara': 'أبو العشرة',
        'abu_ashara': 'أبو العشرة',
        'abuSitta': 'أبو الستة',
        'abu_sitta': 'أبو الستة',
        'other': 'أخرى',
        'stc_pay': 'STC Pay',
        'apple_pay': 'Apple Pay',
        'samsung_pay': 'Samsung Pay',
        'paypal': 'PayPal',
        'bank_transfer': 'تحويل بنكي',
        'check': 'شيك',
        'credit': 'ائتمان',
        'debit': 'خصم مباشر',
        'gift_card': 'بطاقة هدية',
        'loyalty_points': 'نقاط الولاء',
      };

      // البحث عن الترجمة المباشرة
      if (translations.containsKey(cardType.toLowerCase())) {
        final translatedName = translations[cardType.toLowerCase()]!;
        debugPrint('✅ تم ترجمة الكارت: $cardType -> $translatedName');
        return translatedName;
      }

      // محاولة البحث في enum الافتراضي
      try {
        final cardTypeEnum = CardType.values.firstWhere(
          (ct) => ct.name.toLowerCase() == cardType.toLowerCase(),
          orElse: () => CardType.other,
        );
        debugPrint('✅ تم العثور على كارت افتراضي: ${cardTypeEnum.displayName}');
        return cardTypeEnum.displayName;
      } catch (e) {
        debugPrint('⚠️ لم يتم العثور على ترجمة للكارت: $cardType');
        // إذا لم نجد ترجمة، نعيد النص الأصلي
        return cardType;
      }
    }
  }

  // تنسيق التاريخ مع اسم اليوم والوقت
  String _formatDateWithDayAndTime(DateTime date) {
    final dayNames = [
      'الأحد',
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
    ];

    final monthNames = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر',
    ];

    final dayName = dayNames[date.weekday % 7];
    final monthName = monthNames[date.month - 1];

    // تحديد صباح أم مساء
    final hour = date.hour;
    final minute = date.minute;
    String period;
    int displayHour;

    if (hour == 0) {
      displayHour = 12;
      period = 'صباحاً';
    } else if (hour < 12) {
      displayHour = hour;
      period = 'صباحاً';
    } else if (hour == 12) {
      displayHour = 12;
      period = 'ظهراً';
    } else {
      displayHour = hour - 12;
      period = 'مساءً';
    }

    return '$dayName ${date.day} $monthName ${date.year} - $displayHour:${minute.toString().padLeft(2, '0')} $period';
  }

  // تنسيق التاريخ مع اسم اليوم فقط (للاستحقاق)
  String _formatDateWithDay(DateTime date) {
    final dayNames = [
      'الأحد',
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
    ];

    final monthNames = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر',
    ];

    final dayName = dayNames[date.weekday % 7];
    final monthName = monthNames[date.month - 1];

    return '$dayName ${date.day} $monthName ${date.year}';
  }

  // الحصول على إحصائيات التنبيهات
  Map<String, int> getNotificationStats() {
    return {
      'total': _notifications.length,
      'unread': unreadCount,
      'overdue': getNotificationsByType(NotificationType.overdue).length,
      'dueToday': getNotificationsByType(NotificationType.dueToday).length,
      'dueSoon': getNotificationsByType(NotificationType.dueSoon).length,
      'lowStock': getNotificationsByType(NotificationType.lowStock).length,
      'outOfStock': getNotificationsByType(NotificationType.outOfStock).length,
    };
  }
}

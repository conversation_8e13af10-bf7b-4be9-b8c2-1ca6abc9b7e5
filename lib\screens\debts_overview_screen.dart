import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/customer.dart';
import '../models/debt.dart';
import '../providers/customer_provider.dart';
import '../providers/debt_provider.dart';
import '../providers/customer_statistics_provider.dart';
import '../widgets/debt_card.dart';
import '../widgets/customer_statistics_widget.dart';
import 'customer_detail_screen.dart';

// أنواع عرض البطاقات
enum DebtOverviewViewType {
  standard('عادي'),
  compact('مضغوط');

  const DebtOverviewViewType(this.label);
  final String label;
}

class DebtsOverviewScreen extends StatefulWidget {
  const DebtsOverviewScreen({super.key});

  @override
  State<DebtsOverviewScreen> createState() => _DebtsOverviewScreenState();
}

class _DebtsOverviewScreenState extends State<DebtsOverviewScreen> {
  final TextEditingController _searchController = TextEditingController();
  Map<Customer, List<Debt>> _customerDebtsMap = {};
  List<Customer> _filteredCustomers = [];
  bool _isLoading = true;
  final Map<String, bool> _expandedStatistics = {};
  DebtOverviewViewType _currentViewType = DebtOverviewViewType.standard;

  @override
  void initState() {
    super.initState();
    _loadSavedViewType();
    _loadData();

    // الاستماع لتغييرات DebtProvider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final debtProvider = Provider.of<DebtProvider>(context, listen: false);
      debtProvider.addListener(_onDebtProviderChanged);
    });
  }

  // دالة للاستجابة لتغييرات DebtProvider
  void _onDebtProviderChanged() {
    if (mounted) {
      // تحديث فوري بدون تأخير
      _updateCustomerDebtsMap();
    }
  }

  // دالة تحديث ذكية للديون
  void _updateCustomerDebtsMap() {
    try {
      final customerProvider = Provider.of<CustomerProvider>(
        context,
        listen: false,
      );
      final debtProvider = Provider.of<DebtProvider>(context, listen: false);

      // تجميع الديون حسب العملاء
      final Map<Customer, List<Debt>> customerDebtsMap = {};

      for (final customer in customerProvider.customers) {
        final customerDebts = debtProvider.debts
            .where((debt) => debt.customerId == customer.id)
            .toList();

        if (customerDebts.isNotEmpty) {
          customerDebtsMap[customer] = customerDebts;
        }
      }

      // تحديث فوري بدون دائرة تحميل
      if (mounted) {
        setState(() {
          _customerDebtsMap = customerDebtsMap;
          _filteredCustomers = customerDebtsMap.keys.toList();
        });
      }
    } catch (e) {
      // تجاهل الأخطاء للحفاظ على الاستقرار
      debugPrint('Error updating customer debts map: $e');
    }
  }

  @override
  void dispose() {
    _searchController.dispose();

    // إزالة المستمع
    try {
      final debtProvider = Provider.of<DebtProvider>(context, listen: false);
      debtProvider.removeListener(_onDebtProviderChanged);
    } catch (e) {
      // تجاهل الأخطاء عند التخلص من الموارد
    }

    super.dispose();
  }

  // تحميل نوع العرض المحفوظ
  Future<void> _loadSavedViewType() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedViewType = prefs.getString('debt_overview_view_type');

      if (savedViewType != null) {
        final viewType = DebtOverviewViewType.values.firstWhere(
          (e) => e.name == savedViewType,
          orElse: () => DebtOverviewViewType.standard,
        );

        setState(() {
          _currentViewType = viewType;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل نوع العرض: $e');
    }
  }

  // حفظ نوع العرض المختار
  Future<void> _saveViewType(DebtOverviewViewType viewType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('debt_overview_view_type', viewType.name);
    } catch (e) {
      debugPrint('خطأ في حفظ نوع العرض: $e');
    }
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final customerProvider = Provider.of<CustomerProvider>(
        context,
        listen: false,
      );
      final debtProvider = Provider.of<DebtProvider>(context, listen: false);
      final statisticsProvider = Provider.of<CustomerStatisticsProvider>(
        context,
        listen: false,
      );

      // تحميل العملاء والديون والمدفوعات
      await Future.wait([
        customerProvider.loadCustomers(),
        debtProvider.loadAllDebts(),
        debtProvider.loadAllPayments(),
      ]);

      // تحميل الإحصائيات
      await statisticsProvider.calculateStatistics(customerProvider.customers);

      // تجميع الديون حسب العملاء
      final Map<Customer, List<Debt>> customerDebtsMap = {};

      for (final customer in customerProvider.customers) {
        final customerDebts = debtProvider.debts
            .where((debt) => debt.customerId == customer.id)
            .toList();

        if (customerDebts.isNotEmpty) {
          customerDebtsMap[customer] = customerDebts;
        }
      }

      setState(() {
        _customerDebtsMap = customerDebtsMap;
        _filteredCustomers = customerDebtsMap.keys.toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _filterCustomers(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredCustomers = _customerDebtsMap.keys.toList();
      } else {
        _filteredCustomers = _customerDebtsMap.keys.where((customer) {
          return customer.name.toLowerCase().contains(query.toLowerCase()) ||
              (customer.phone?.contains(query) ?? false);
        }).toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          'نظرة عامة للديون',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          // زر نوع العرض
          PopupMenuButton<DebtOverviewViewType>(
            icon: Icon(
              _currentViewType == DebtOverviewViewType.compact
                  ? Icons.view_compact
                  : Icons.view_comfortable,
              size: 20,
            ),
            tooltip: 'نوع العرض',
            onSelected: (viewType) {
              setState(() {
                _currentViewType = viewType;
              });
              _saveViewType(viewType);
            },
            itemBuilder: (context) =>
                DebtOverviewViewType.values.map((viewType) {
              return PopupMenuItem<DebtOverviewViewType>(
                value: viewType,
                child: Row(
                  children: [
                    Icon(
                      viewType == DebtOverviewViewType.compact
                          ? Icons.view_compact
                          : Icons.view_comfortable,
                      size: 18,
                      color: _currentViewType == viewType
                          ? Colors.blue.shade600
                          : Colors.grey.shade600,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      viewType.label,
                      style: TextStyle(
                        color: _currentViewType == viewType
                            ? Colors.blue.shade600
                            : Colors.black87,
                        fontWeight: _currentViewType == viewType
                            ? FontWeight.w600
                            : FontWeight.normal,
                      ),
                    ),
                    if (_currentViewType == viewType) ...[
                      const Spacer(),
                      Icon(
                        Icons.check,
                        size: 16,
                        color: Colors.blue.shade600,
                      ),
                    ],
                  ],
                ),
              );
            }).toList(),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث
          _buildSearchBar(),

          // عدد العملاء والديون
          _buildStatistics(),

          // قائمة الديون مصنفة حسب العملاء
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredCustomers.isEmpty
                    ? _buildEmptyState()
                    : _buildCustomerDebtsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: StatefulBuilder(
        builder: (context, setSearchState) {
          return TextField(
            controller: _searchController,
            onChanged: (value) {
              _filterCustomers(value);
              setSearchState(() {}); // تحديث حالة زر المسح
            },
            style: const TextStyle(
              color: Colors.black87,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
            textDirection: TextDirection.rtl,
            decoration: InputDecoration(
              hintText: 'البحث عن عميل...',
              hintStyle: TextStyle(
                color: Colors.grey.shade500,
                fontSize: 16,
                fontWeight: FontWeight.normal,
              ),
              prefixIcon: Icon(Icons.search, color: Colors.blue.shade600),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: Icon(Icons.clear, color: Colors.red.shade400),
                      onPressed: () {
                        _searchController.clear();
                        _filterCustomers('');
                        setSearchState(() {}); // تحديث حالة زر المسح
                      },
                      tooltip: 'مسح البحث',
                    )
                  : null,
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatistics() {
    final totalDebts = _customerDebtsMap.values.expand((debts) => debts).length;
    final hasExpandedStatistics = _expandedStatistics.values.any(
      (expanded) => expanded,
    );

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color:
            hasExpandedStatistics ? Colors.orange.shade50 : Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: hasExpandedStatistics
              ? Colors.orange.shade200
              : Colors.blue.shade200,
        ),
      ),
      child: Row(
        children: [
          Icon(
            hasExpandedStatistics ? Icons.analytics : Icons.receipt_long,
            color: hasExpandedStatistics
                ? Colors.orange.shade600
                : Colors.blue.shade600,
            size: 16,
          ),
          const SizedBox(width: 6),
          Expanded(
            child: Text(
              hasExpandedStatistics
                  ? 'عرض إحصائيات مفصلة - اضغط على السهم لإغلاق الإحصائيات'
                  : 'العملاء: ${_filteredCustomers.length} • الديون: $totalDebts',
              style: TextStyle(
                fontSize: 11,
                fontWeight: FontWeight.w600,
                color: hasExpandedStatistics
                    ? Colors.orange.shade700
                    : Colors.blue.shade700,
              ),
            ),
          ),
          if (hasExpandedStatistics)
            Icon(
              Icons.keyboard_arrow_up,
              color: Colors.orange.shade600,
              size: 16,
            ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long_outlined,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد ديون',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ بإضافة ديون للعملاء',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerDebtsList() {
    // التحقق من وجود إحصائيات مفتوحة
    final hasExpandedStatistics = _expandedStatistics.values.any(
      (expanded) => expanded,
    );

    if (hasExpandedStatistics) {
      // إذا كانت هناك إحصائيات مفتوحة، عرض العميل الذي له إحصائيات مفتوحة فقط
      final expandedCustomerId =
          _expandedStatistics.entries.firstWhere((entry) => entry.value).key;

      final expandedCustomer = _filteredCustomers.firstWhere(
        (customer) => customer.id!.toString() == expandedCustomerId,
      );

      final customerDebts = _customerDebtsMap[expandedCustomer] ?? [];
      final customerIndex = _filteredCustomers.indexOf(expandedCustomer);

      return RefreshIndicator(
        onRefresh: _loadData,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(8),
          child: _buildCustomerSection(
            expandedCustomer,
            customerDebts,
            customerIndex,
          ),
        ),
      );
    }

    // العرض العادي عندما لا توجد إحصائيات مفتوحة
    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(8),
        itemCount: _filteredCustomers.length,
        itemBuilder: (context, index) {
          final customer = _filteredCustomers[index];
          final customerDebts = _customerDebtsMap[customer] ?? [];

          return _buildCustomerSection(customer, customerDebts, index);
        },
      ),
    );
  }

  Widget _buildCustomerSection(
    Customer customer,
    List<Debt> debts,
    int customerIndex,
  ) {
    final customerId = customer.id!.toString();
    final isCurrentCustomerExpanded = _expandedStatistics[customerId] ?? false;

    // التحقق من وجود أي إحصائيات مفتوحة
    final hasAnyExpandedStatistics = _expandedStatistics.values.any(
      (expanded) => expanded,
    );

    // عند وجود إحصائيات مفتوحة، عرض بطاقات الديون فقط للعميل الذي له إحصائيات مفتوحة
    final shouldShowCards =
        !hasAnyExpandedStatistics || isCurrentCustomerExpanded;

    double dragStartX = 0.0;
    double dragStartY = 0.0;
    bool hasTriggered = false;
    bool isHorizontalDrag = false;

    return GestureDetector(
      // تقليل الحساسية للسحب الأفقي
      behavior: HitTestBehavior.opaque,
      onPanStart: (details) {
        dragStartX = details.globalPosition.dx;
        dragStartY = details.globalPosition.dy;
        hasTriggered = false;
        isHorizontalDrag = false;
      },
      onPanUpdate: (details) {
        if (!hasTriggered) {
          final dragDistanceX = dragStartX - details.globalPosition.dx;
          final dragDistanceY = (dragStartY - details.globalPosition.dy).abs();
          final totalDistance =
              dragDistanceX * dragDistanceX + dragDistanceY * dragDistanceY;

          // التأكد من أن السحب أفقي بشكل واضح
          if (dragDistanceX > 20 &&
              dragDistanceY < 40 &&
              dragDistanceX > dragDistanceY * 1.5) {
            isHorizontalDrag = true;

            // استجابة عند السحب لمسافة 100 بكسل مع التأكد من الاتجاه الأفقي
            if (dragDistanceX > 100 && totalDistance > 10000) {
              hasTriggered = true;
              _showCustomerDetails(customer);
            }
          }
        }
      },
      onPanEnd: (details) {
        if (!hasTriggered && isHorizontalDrag) {
          // التحقق من اتجاه السحب وسرعة معتدلة
          if (details.velocity.pixelsPerSecond.dx < -400) {
            _showCustomerDetails(customer);
          }
        }
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس العميل
            _buildCustomerHeader(customer, debts),

            // إحصائيات العميل
            _buildCustomerStatistics(customer),

            // شبكة بطاقات الديون (2 في الصف) - تظهر فقط عندما لا توجد إحصائيات مفتوحة أو للعميل الحالي
            if (shouldShowCards) _buildDebtsGrid(debts),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerHeader(Customer customer, List<Debt> debts) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.purple.shade600,
            Colors.deepPurple.shade700,
            Colors.indigo.shade800,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          stops: const [0.0, 0.5, 1.0],
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.purple.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          // أيقونة العميل مصغرة
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: const Icon(Icons.person, color: Colors.white, size: 18),
          ),

          const SizedBox(width: 10),

          // معلومات العميل مع رقم الهاتف
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  customer.name,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    Text(
                      'عدد الديون: ${debts.length}',
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.white.withValues(alpha: 0.9),
                      ),
                    ),
                    if (customer.phone != null &&
                        customer.phone!.isNotEmpty) ...[
                      Text(
                        ' • ',
                        style: TextStyle(
                          fontSize: 11,
                          color: Colors.white.withValues(alpha: 0.7),
                        ),
                      ),
                      Text(
                        customer.phone!,
                        style: TextStyle(
                          fontSize: 11,
                          color: Colors.white.withValues(alpha: 0.9),
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),

          // زر الاتصال
          if (customer.phone != null && customer.phone!.isNotEmpty)
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.teal.shade500, Colors.cyan.shade600],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.teal.withValues(alpha: 0.4),
                    blurRadius: 6,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: IconButton(
                onPressed: () => _makePhoneCall(customer.phone!),
                icon: const Icon(Icons.phone, color: Colors.white, size: 18),
                tooltip: 'اتصال بـ ${customer.name}',
                padding: const EdgeInsets.all(8),
                constraints: const BoxConstraints(minWidth: 36, minHeight: 36),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildCustomerStatistics(Customer customer) {
    return Consumer<CustomerStatisticsProvider>(
      builder: (context, statisticsProvider, _) {
        final statistics =
            statisticsProvider.customerStatistics[customer.id!.toString()];

        if (statistics == null) {
          return const SizedBox.shrink();
        }

        final customerId = customer.id!.toString();
        final isExpanded = _expandedStatistics[customerId] ?? false;

        return CustomerStatisticsWidget(
          statistics: statistics,
          isExpanded: isExpanded,
          onToggle: () {
            setState(() {
              _expandedStatistics[customerId] = !isExpanded;
            });
          },
        );
      },
    );
  }

  Widget _buildDebtsGrid(List<Debt> debts) {
    if (debts.isEmpty) {
      return const Padding(
        padding: EdgeInsets.all(16),
        child: Center(
          child: Text(
            'لا توجد ديون',
            style: TextStyle(color: Colors.grey, fontSize: 14),
          ),
        ),
      );
    }

    // تنظيم الديون حسب التاريخ
    final organizedDebts = _organizeDebtsByDate(debts);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4.0, vertical: 4.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: organizedDebts.entries.map((entry) {
          final dateCategory = entry.key;
          final categoryDebts = entry.value;

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان الفئة الزمنية
              Container(
                margin: const EdgeInsets.only(bottom: 8, top: 12),
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: _getDateCategoryColors(dateCategory),
                  ),
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: _getDateCategoryColors(
                        dateCategory,
                      )[0]
                          .withValues(alpha: 0.3),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _getDateCategoryIcon(dateCategory),
                      color: Colors.white,
                      size: 16,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      dateCategory,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(width: 6),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        '${categoryDebts.length}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // شبكة البطاقات للفئة
              _currentViewType == DebtOverviewViewType.compact
                  ? _buildCompactDebtsList(categoryDebts)
                  : LayoutBuilder(
                      builder: (context, constraints) {
                        final screenWidth = constraints.maxWidth;
                        const spacing = 4.0;
                        final cardWidth = (screenWidth - spacing) / 2;

                        return Wrap(
                          spacing: spacing,
                          runSpacing: 4.0,
                          children: categoryDebts.map((debt) {
                            return SizedBox(
                              width: cardWidth,
                              child: DebtCard(debt: debt, isGridView: true),
                            );
                          }).toList(),
                        );
                      },
                    ),

              const SizedBox(height: 8),
            ],
          );
        }).toList(),
      ),
    );
  }

  // دالة العرض المضغوط للديون
  Widget _buildCompactDebtsList(List<Debt> debts) {
    return Column(
      children: debts.map((debt) {
        return Container(
          margin: const EdgeInsets.only(bottom: 4),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.shade200),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.05),
                blurRadius: 2,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Row(
            children: [
              // أيقونة نوع الدين
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  Icons.receipt_long,
                  color: Colors.blue.shade600,
                  size: 16,
                ),
              ),
              const SizedBox(width: 8),

              // تفاصيل الدين
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      debt.itemName,
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (debt.notes?.isNotEmpty == true) ...[
                      const SizedBox(height: 2),
                      Text(
                        debt.notes!,
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.grey.shade600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),

              // المبلغ والكمية
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '${(debt.amount / 1000).toStringAsFixed(3)} ألف',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.green.shade700,
                    ),
                  ),
                  Text(
                    'الكمية: ${debt.quantity}',
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  // دالة تنظيم الديون حسب التاريخ
  Map<String, List<Debt>> _organizeDebtsByDate(List<Debt> debts) {
    final Map<String, List<Debt>> organized = {};
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final thisWeek = today.subtract(const Duration(days: 7));
    final thisMonth = DateTime(now.year, now.month);

    for (final debt in debts) {
      final debtDate = DateTime(
        debt.entryDate.year,
        debt.entryDate.month,
        debt.entryDate.day,
      );
      String category;

      if (debtDate.isAtSameMomentAs(today)) {
        category = 'اليوم';
      } else if (debtDate.isAtSameMomentAs(yesterday)) {
        category = 'أمس';
      } else if (debtDate.isAfter(thisWeek)) {
        category = 'هذا الأسبوع';
      } else if (debtDate.isAfter(thisMonth)) {
        category = 'هذا الشهر';
      } else {
        category = 'أقدم';
      }

      organized.putIfAbsent(category, () => []);
      organized[category]!.add(debt);
    }

    // ترتيب الفئات حسب الأولوية
    final orderedCategories = [
      'اليوم',
      'أمس',
      'هذا الأسبوع',
      'هذا الشهر',
      'أقدم',
    ];
    final Map<String, List<Debt>> orderedResult = {};

    for (final category in orderedCategories) {
      if (organized.containsKey(category)) {
        // ترتيب الديون داخل كل فئة حسب التاريخ (الأحدث أولاً)
        organized[category]!.sort((a, b) => b.entryDate.compareTo(a.entryDate));
        orderedResult[category] = organized[category]!;
      }
    }

    return orderedResult;
  }

  // دالة الحصول على ألوان الفئة الزمنية
  List<Color> _getDateCategoryColors(String category) {
    switch (category) {
      case 'اليوم':
        return [Colors.green.shade500, Colors.green.shade700];
      case 'أمس':
        return [Colors.blue.shade500, Colors.blue.shade700];
      case 'هذا الأسبوع':
        return [Colors.orange.shade500, Colors.orange.shade700];
      case 'هذا الشهر':
        return [Colors.purple.shade500, Colors.purple.shade700];
      case 'أقدم':
        return [Colors.grey.shade500, Colors.grey.shade700];
      default:
        return [Colors.grey.shade500, Colors.grey.shade700];
    }
  }

  // دالة الحصول على أيقونة الفئة الزمنية
  IconData _getDateCategoryIcon(String category) {
    switch (category) {
      case 'اليوم':
        return Icons.today;
      case 'أمس':
        return Icons.schedule;
      case 'هذا الأسبوع':
        return Icons.date_range;
      case 'هذا الشهر':
        return Icons.calendar_month;
      case 'أقدم':
        return Icons.history;
      default:
        return Icons.schedule;
    }
  }

  // دالة الاتصال
  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);
    try {
      if (await canLaunchUrl(phoneUri)) {
        await launchUrl(phoneUri);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('لا يمكن إجراء المكالمة'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في الاتصال: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // دالة عرض تفاصيل العميل
  Future<void> _showCustomerDetails(Customer customer) async {
    // الانتقال إلى صفحة تفاصيل العميل
    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CustomerDetailScreen(customer: customer),
      ),
    );

    // تحديث ذكي بدون دائرة تحميل
    if (mounted) {
      _updateCustomerDebtsMap();
    }
  }
}

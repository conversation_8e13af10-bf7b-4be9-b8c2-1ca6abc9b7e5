import 'package:flutter/foundation.dart';
import '../models/customer.dart';
import '../database/database_helper.dart';

class CustomerProvider with ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  List<Customer> _customers = [];
  List<Customer> _filteredCustomers = [];
  bool _isLoading = false;
  String _searchQuery = '';

  List<Customer> get customers => _filteredCustomers;
  bool get isLoading => _isLoading;
  String get searchQuery => _searchQuery;

  Future<void> loadCustomers() async {
    _isLoading = true;
    notifyListeners();

    try {
      _customers = await _databaseHelper.getAllCustomers();
      _filteredCustomers = List.from(_customers);
    } catch (e) {
      debugPrint('Error loading customers: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // نسخة صامتة لا تستدعي notifyListeners أثناء البناء
  Future<void> loadCustomersSilently() async {
    try {
      _customers = await _databaseHelper.getAllCustomers();
      _filteredCustomers = List.from(_customers);
      _isLoading = false;
    } catch (e) {
      debugPrint('Error loading customers: $e');
      _isLoading = false;
    }
  }

  Future<Customer> addCustomer(Customer customer) async {
    try {
      final id = await _databaseHelper.insertCustomer(customer);
      final newCustomer = customer.copyWith(id: id);
      _customers.add(newCustomer);
      _applyFilter();
      notifyListeners();
      return newCustomer;
    } catch (e) {
      debugPrint('Error adding customer: $e');
      rethrow;
    }
  }

  Future<void> updateCustomer(Customer customer) async {
    try {
      // تحديث قاعدة البيانات أولاً
      await _databaseHelper.updateCustomer(customer);

      // تحديث القائمة المحلية
      final index = _customers.indexWhere((c) => c.id == customer.id);
      if (index != -1) {
        _customers[index] = customer;
        _applyFilter();
        notifyListeners();
      }

      // طباعة للتأكد من التحديث (للتطوير فقط)
      debugPrint(
        'Customer updated successfully: ID=${customer.id}, Phone=${customer.phone}',
      );
    } catch (e) {
      debugPrint('Error updating customer: $e');
      rethrow;
    }
  }

  Future<void> deleteCustomer(int id) async {
    try {
      await _databaseHelper.deleteCustomer(id);
      _customers.removeWhere((customer) => customer.id == id);
      _applyFilter();
      notifyListeners();
    } catch (e) {
      debugPrint('Error deleting customer: $e');
      rethrow;
    }
  }

  void searchCustomers(
    String query, {
    String searchType = 'name',
    DateTime? fromDate,
    DateTime? toDate,
  }) {
    _searchQuery = query;
    _applyFilter(searchType: searchType, fromDate: fromDate, toDate: toDate);
    notifyListeners();
  }

  void _applyFilter({
    String searchType = 'name',
    DateTime? fromDate,
    DateTime? toDate,
  }) {
    if (_searchQuery.isEmpty && fromDate == null && toDate == null) {
      _filteredCustomers = List.from(_customers);
    } else {
      _filteredCustomers = _customers.where((customer) {
        bool matchesQuery = true;
        bool matchesDate = true;

        // البحث النصي
        if (_searchQuery.isNotEmpty) {
          final query = _searchQuery.toLowerCase();
          switch (searchType) {
            case 'name':
              matchesQuery = customer.name.toLowerCase().contains(query);
              break;
            case 'phone':
              matchesQuery = customer.phone?.contains(query) ?? false;
              break;
            case 'all':
              matchesQuery =
                  customer.name.toLowerCase().contains(query) ||
                  (customer.phone?.contains(query) ?? false);
              break;
          }
        }

        // البحث بالتاريخ
        if (fromDate != null || toDate != null) {
          final customerDate = customer.createdAt;
          if (fromDate != null && toDate != null) {
            matchesDate =
                customerDate.isAfter(
                  fromDate.subtract(const Duration(days: 1)),
                ) &&
                customerDate.isBefore(toDate.add(const Duration(days: 1)));
          } else if (fromDate != null) {
            matchesDate = customerDate.isAfter(
              fromDate.subtract(const Duration(days: 1)),
            );
          } else if (toDate != null) {
            matchesDate = customerDate.isBefore(
              toDate.add(const Duration(days: 1)),
            );
          }
        }

        return matchesQuery && matchesDate;
      }).toList();
    }
  }

  Customer? getCustomerById(int id) {
    try {
      return _customers.firstWhere((customer) => customer.id == id);
    } catch (e) {
      return null;
    }
  }

  void clearSearch() {
    _searchQuery = '';
    _filteredCustomers = List.from(_customers);
    notifyListeners();
  }

  void setFilteredCustomers(List<Customer> customers) {
    _filteredCustomers = customers;
    notifyListeners();
  }

  // حساب المبلغ الإجمالي للعميل (الديون - المدفوعات)
  Future<double> getCustomerTotalAmount(int customerId) async {
    try {
      final debts = await _databaseHelper.getCustomerDebts(customerId);
      final payments = await _databaseHelper.getCustomerPayments(customerId);

      final totalDebts = debts.fold<double>(
        0,
        (sum, debt) => sum + debt.amount,
      );
      final totalPayments = payments.fold<double>(
        0,
        (sum, payment) => sum + payment.amount,
      );

      return totalDebts - totalPayments;
    } catch (e) {
      debugPrint('Error calculating customer total amount: $e');
      return 0.0;
    }
  }
}

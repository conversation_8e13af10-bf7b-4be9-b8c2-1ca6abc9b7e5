import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/customer_provider.dart';
import '../models/customer.dart';
import '../screens/customer_detail_screen.dart';

class SearchBarWidget extends StatefulWidget {
  const SearchBarWidget({
    super.key,
    this.onCustomerSelected,
    this.onClearSearch,
  });
  final Function(Customer)? onCustomerSelected;
  final VoidCallback? onClearSearch;

  @override
  State<SearchBarWidget> createState() => _SearchBarWidgetState();
}

class _SearchBarWidgetState extends State<SearchBarWidget>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  bool _isSearching = false;
  bool _isSearchFocused = false;
  DateTime? _fromDate;
  DateTime? _toDate;
  String _selectedSearchType = 'name'; // name, phone, date, amount
  String _selectedFilter = 'الكل';

  // Animation controllers
  late AnimationController _searchAnimationController;
  late Animation<double> _searchAnimation;

  // Customer data
  int _recentCustomers = 0;
  List<Customer> _allCustomers = [];

  @override
  void initState() {
    super.initState();

    // إضافة مراقب لحالة التطبيق
    WidgetsBinding.instance.addObserver(this);

    // Initialize animation controller
    _searchAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _searchAnimation = CurvedAnimation(
      parent: _searchAnimationController,
      curve: Curves.fastOutSlowIn,
    );

    // Setup focus listener
    _searchFocusNode.addListener(_onFocusChanged);

    // Load initial data after build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadCustomerData();
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _searchController.dispose();
    _searchFocusNode.dispose();
    _searchAnimationController.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // عند العودة للتطبيق من الخلفية، امسح البحث
    if (state == AppLifecycleState.resumed) {
      // تأخير قصير للتأكد من اكتمال العودة
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted && _searchController.text.isNotEmpty) {
          _clearSearch();
        }
      });
    }
  }

  void _onFocusChanged() {
    setState(() {
      _isSearchFocused = _searchFocusNode.hasFocus;
    });

    if (_isSearchFocused) {
      _searchAnimationController.forward();
    } else {
      _searchAnimationController.reverse();
    }
  }

  Future<void> _loadCustomerData() async {
    if (mounted) {
      final customerProvider = Provider.of<CustomerProvider>(
        context,
        listen: false,
      );
      // استخدام النسخة الصامتة لتجنب notifyListeners أثناء البناء
      await customerProvider.loadCustomersSilently();

      if (mounted) {
        setState(() {
          _allCustomers = customerProvider.customers;
          _calculateStatistics();
        });
      }
    }
  }

  void _calculateStatistics() {
    // العملاء الجدد (آخر 7 أيام) - للفلاتر السريعة
    final weekAgo = DateTime.now().subtract(const Duration(days: 7));
    _recentCustomers =
        _allCustomers.where((c) => c.createdAt.isAfter(weekAgo)).length;
  }

  void _onSearchChanged(String query) {
    setState(() {
      _isSearching = query.isNotEmpty;
    });

    // البحث المتقدم مع نفس منطق "اختر عميل"
    final customerProvider = Provider.of<CustomerProvider>(
      context,
      listen: false,
    );

    if (query.isEmpty) {
      customerProvider.clearSearch();
    } else {
      // استخدام البحث المتقدم
      _performAdvancedSearch(query, customerProvider);
    }
  }

  void _performAdvancedSearch(String query, CustomerProvider customerProvider) {
    final filteredCustomers = _allCustomers.where((customer) {
      final name = customer.name.toLowerCase();
      final phone = customer.phone?.toLowerCase() ?? '';
      final searchQuery = query.toLowerCase().trim();

      // البحث المتقدم مع تحديد الحروف
      return _isAdvancedMatch(name, searchQuery) ||
          _isAdvancedMatch(phone, searchQuery) ||
          _isPhoneNumberMatch(phone, searchQuery) ||
          _isNameMatch(name, searchQuery) ||
          _isFuzzyMatch(name, searchQuery);
    }).toList();

    // ترتيب النتائج حسب الصلة
    filteredCustomers.sort((a, b) {
      final aName = a.name.toLowerCase();
      final bName = b.name.toLowerCase();
      final searchQuery = query.toLowerCase();

      // الأولوية للمطابقة الكاملة
      if (aName.startsWith(searchQuery) && !bName.startsWith(searchQuery)) {
        return -1;
      }
      if (!aName.startsWith(searchQuery) && bName.startsWith(searchQuery)) {
        return 1;
      }

      // ثم الترتيب الأبجدي
      return aName.compareTo(bName);
    });

    // تحديث النتائج
    customerProvider.setFilteredCustomers(filteredCustomers);
  }

  void _clearSearch() {
    _searchController.clear();
    Provider.of<CustomerProvider>(context, listen: false).clearSearch();
    setState(() {
      _isSearching = false;
      _fromDate = null;
      _toDate = null;
      _selectedSearchType = 'name';
    });

    // استدعاء callback إذا كان متوفراً
    if (widget.onClearSearch != null) {
      widget.onClearSearch!();
    }
  }

  void _showAdvancedSearchDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _buildAdvancedSearchDialog(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _searchAnimation,
      builder: (context, child) {
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Enhanced Search Field
              _buildEnhancedSearchField(),

              // Quick Filters (عند البحث)
              if (_isSearching) ...[
                const SizedBox(height: 12),
                _buildQuickFilters(),
              ],

              // تم إزالة بطاقة الاقتراحات الذكية لتوفير المساحة
            ],
          ),
        );
      },
    );
  }

  String _formatDate(DateTime date) {
    return '${date.year}/${date.month}/${date.day}';
  }

  Widget _buildAdvancedSearchDialog() {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 4,
      backgroundColor: Colors.white,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: BoxConstraints(
          maxWidth: 500,
          maxHeight: MediaQuery.of(context).size.height * 0.75,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.white,
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: StatefulBuilder(
          builder: (context, setDialogState) {
            return Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Row(
                    children: [
                      const Expanded(
                        child: Text(
                          'البحث المتقدم',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: Icon(
                          Icons.close,
                          color: Colors.grey.shade600,
                          size: 24,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),

                  // Scrollable content
                  Flexible(
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // نوع البحث
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'نوع البحث:',
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 16,
                                  color: Colors.black87,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Container(
                                width: double.infinity,
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: Colors.grey.shade300,
                                  ),
                                ),
                                child: DropdownButtonHideUnderline(
                                  child: DropdownButton<String>(
                                    value: _selectedSearchType,
                                    isExpanded: true,
                                    style: const TextStyle(
                                      color: Colors.black87,
                                      fontSize: 16,
                                    ),
                                    items: const [
                                      DropdownMenuItem(
                                        value: 'name',
                                        child: Text('الاسم'),
                                      ),
                                      DropdownMenuItem(
                                        value: 'phone',
                                        child: Text('رقم الهاتف'),
                                      ),
                                      DropdownMenuItem(
                                        value: 'all',
                                        child: Text('جميع الحقول'),
                                      ),
                                    ],
                                    onChanged: (value) {
                                      setDialogState(() {
                                        _selectedSearchType = value!;
                                      });
                                    },
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 20),

                          // البحث بالتاريخ
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  const Text(
                                    'البحث بالتاريخ:',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 16,
                                      color: Colors.black87,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 2,
                                    ),
                                    decoration: BoxDecoration(
                                      color: Colors.grey.shade200,
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Text(
                                      'اختياري',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey.shade600,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 12),
                              Row(
                                children: [
                                  Expanded(
                                    child: InkWell(
                                      onTap: () async {
                                        final date = await showDatePicker(
                                          context: context,
                                          initialDate:
                                              _fromDate ?? DateTime.now(),
                                          firstDate: DateTime(2020),
                                          lastDate: DateTime.now(),
                                        );
                                        if (date != null) {
                                          setDialogState(() {
                                            _fromDate = date;
                                          });
                                        }
                                      },
                                      child: Container(
                                        padding: const EdgeInsets.all(12),
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          border: Border.all(
                                            color: Colors.grey.shade300,
                                          ),
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                        ),
                                        child: Row(
                                          children: [
                                            Icon(
                                              Icons.calendar_today,
                                              color: Colors.grey.shade600,
                                              size: 16,
                                            ),
                                            const SizedBox(width: 8),
                                            Expanded(
                                              child: Text(
                                                _fromDate != null
                                                    ? 'من: ${_formatDate(_fromDate!)}'
                                                    : 'من تاريخ',
                                                style: TextStyle(
                                                  color: _fromDate != null
                                                      ? Colors.black87
                                                      : Colors.grey.shade600,
                                                  fontSize: 14,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: InkWell(
                                      onTap: () async {
                                        final date = await showDatePicker(
                                          context: context,
                                          initialDate:
                                              _toDate ?? DateTime.now(),
                                          firstDate: DateTime(2020),
                                          lastDate: DateTime.now(),
                                        );
                                        if (date != null) {
                                          setDialogState(() {
                                            _toDate = date;
                                          });
                                        }
                                      },
                                      child: Container(
                                        padding: const EdgeInsets.all(12),
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          border: Border.all(
                                            color: Colors.grey.shade300,
                                          ),
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                        ),
                                        child: Row(
                                          children: [
                                            Icon(
                                              Icons.calendar_today,
                                              color: Colors.grey.shade600,
                                              size: 16,
                                            ),
                                            const SizedBox(width: 8),
                                            Expanded(
                                              child: Text(
                                                _toDate != null
                                                    ? 'إلى: ${_formatDate(_toDate!)}'
                                                    : 'إلى تاريخ',
                                                style: TextStyle(
                                                  color: _toDate != null
                                                      ? Colors.black87
                                                      : Colors.grey.shade600,
                                                  fontSize: 14,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Action buttons at bottom
                  const SizedBox(height: 24),
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () {
                            setState(() {
                              _selectedSearchType = 'name';
                              _fromDate = null;
                              _toDate = null;
                            });
                            Navigator.pop(context);
                            _onSearchChanged(_searchController.text);
                          },
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            side: BorderSide(color: Colors.grey.shade400),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text('إعادة تعيين'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () => Navigator.pop(context),
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            side: BorderSide(color: Colors.grey.shade400),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text('إلغاء'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.pop(context);
                            setState(() {});
                            _onSearchChanged(_searchController.text);
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            elevation: 1,
                          ),
                          child: const Text('تطبيق'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildEnhancedSearchField() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: _isSearchFocused
                ? Colors.blue.withValues(alpha: 0.2)
                : Colors.grey.withValues(alpha: 0.1),
            blurRadius: _isSearchFocused ? 8 : 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        focusNode: _searchFocusNode,
        style: const TextStyle(
          color: Colors.black87,
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
        decoration: InputDecoration(
          hintText: 'ابحث عن عميل بالاسم أو رقم الهاتف...',
          hintStyle: TextStyle(color: Colors.grey.shade500, fontSize: 14),
          prefixIcon: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            margin: const EdgeInsets.all(8),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: _isSearchFocused
                    ? [Colors.blue.shade400, Colors.purple.shade400]
                    : [Colors.grey.shade200, Colors.grey.shade300],
              ),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              Icons.search_rounded,
              color: _isSearchFocused ? Colors.white : Colors.grey.shade600,
              size: 20,
            ),
          ),
          suffixIcon: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (_isSearching) ...[
                // Results Counter
                Container(
                  margin: const EdgeInsets.only(right: 8),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.blue.shade100, Colors.purple.shade100],
                    ),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.person_search_rounded,
                        size: 14,
                        color: Colors.blue.shade700,
                      ),
                      const SizedBox(width: 4),
                      Consumer<CustomerProvider>(
                        builder: (context, provider, child) {
                          return Text(
                            '${provider.customers.length}',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue.shade700,
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
                // Clear Button
                Container(
                  margin: const EdgeInsets.only(right: 4),
                  decoration: BoxDecoration(
                    color: Colors.red.shade50,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: IconButton(
                    onPressed: _clearSearch,
                    icon: Icon(
                      Icons.clear_rounded,
                      color: Colors.red.shade600,
                      size: 18,
                    ),
                    tooltip: 'مسح البحث',
                  ),
                ),
              ] else ...[
                // Advanced Search Button
                Container(
                  margin: const EdgeInsets.only(right: 4),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: IconButton(
                    onPressed: _showAdvancedSearchDialog,
                    icon: Icon(
                      Icons.tune_rounded,
                      color: Colors.blue.shade600,
                      size: 18,
                    ),
                    tooltip: 'البحث المتقدم',
                  ),
                ),
              ],
            ],
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(color: Colors.blue.shade400, width: 2),
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 14,
          ),
        ),
        onChanged: _onSearchChanged,
      ),
    );
  }

  Widget _buildQuickFilters() {
    return SizedBox(
      height: 40,
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          _buildFilterChip(
            'الكل',
            _allCustomers.length,
            _selectedFilter == 'الكل',
            () => setState(() => _selectedFilter = 'الكل'),
          ),
          const SizedBox(width: 8),
          _buildFilterChip(
            'لديه هاتف',
            _allCustomers.where((c) => c.phone != null).length,
            _selectedFilter == 'لديه هاتف',
            () => setState(() => _selectedFilter = 'لديه هاتف'),
          ),
          const SizedBox(width: 8),
          _buildFilterChip(
            'جديد',
            _recentCustomers,
            _selectedFilter == 'جديد',
            () => setState(() => _selectedFilter = 'جديد'),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(
    String label,
    int count,
    bool isSelected,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          gradient: isSelected
              ? LinearGradient(
                  colors: [Colors.blue.shade400, Colors.purple.shade400],
                )
              : null,
          color: isSelected ? null : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? Colors.transparent : Colors.grey.shade300,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: Colors.blue.withValues(alpha: 0.3),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                color: isSelected ? Colors.white : Colors.grey.shade700,
              ),
            ),
            const SizedBox(width: 6),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: isSelected
                    ? Colors.white.withValues(alpha: 0.3)
                    : Colors.grey.shade200,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                count.toString(),
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: isSelected ? Colors.white : Colors.grey.shade600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchSuggestions() {
    final suggestions = _getSearchSuggestions();

    if (suggestions.isEmpty) return const SizedBox.shrink();

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.blue.shade50, Colors.purple.shade50],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.blue.shade200, width: 1.5),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
            spreadRadius: 1,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with enhanced design
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.blue.shade400, Colors.purple.shade400],
                  ),
                  borderRadius: BorderRadius.circular(10),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.blue.withValues(alpha: 0.3),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.auto_awesome_rounded,
                  size: 18,
                  color: Colors.white,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'اقتراحات ذكية',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue.shade800,
                      ),
                    ),
                    Text(
                      'اختر من الاقتراحات أو اكتب للبحث',
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              // Results count
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Text(
                  '${suggestions.length}',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade700,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Enhanced suggestions grid
          _buildSuggestionsGrid(suggestions),

          // Quick actions
          const SizedBox(height: 12),
          _buildQuickActions(),
        ],
      ),
    );
  }

  Widget _buildSuggestionsGrid(List<String> suggestions) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: suggestions.asMap().entries.map((entry) {
        final index = entry.key;
        final suggestion = entry.value;

        return GestureDetector(
          onTap: () {
            _searchController.text = suggestion;
            _onSearchChanged(suggestion);

            // البحث عن العميل المطابق للاقتراح
            final matchingCustomer = _allCustomers.firstWhere(
              (customer) => customer.name == suggestion,
              orElse: () => _allCustomers.first,
            );

            // إذا وُجد عميل مطابق، انتقل إلى صفحة تفاصيله
            if (_allCustomers.any((customer) => customer.name == suggestion)) {
              // استخدام callback إذا كان متوفراً، وإلا استخدم التنقل المباشر
              if (widget.onCustomerSelected != null) {
                widget.onCustomerSelected!(matchingCustomer);
                _clearSearch();
              } else {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) =>
                        CustomerDetailScreen(customer: matchingCustomer),
                  ),
                ).then((_) {
                  // عند العودة من صفحة العميل، امسح البحث
                  _clearSearch();
                });
              }
            }
          },
          child: AnimatedContainer(
            duration: Duration(milliseconds: 200 + (index * 50)),
            curve: Curves.easeOutBack,
            padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 10),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade300),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  blurRadius: 3,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Text(
              suggestion,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey.shade700,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildQuickActions() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          Icon(Icons.tips_and_updates, size: 16, color: Colors.orange.shade600),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'نصيحة: يمكنك البحث بالاسم أو رقم الهاتف',
              style: TextStyle(
                fontSize: 11,
                color: Colors.grey.shade700,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              _searchController.clear();
              _searchFocusNode.unfocus();
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.clear, size: 12, color: Colors.grey.shade600),
                  const SizedBox(width: 4),
                  Text(
                    'مسح',
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<String> _getSearchSuggestions() {
    final query = _searchController.text;
    if (query.length < 2) return [];

    final suggestions = <String>{};
    final customerNames = <String>{};

    // Add name suggestions with priority
    for (final customer in _allCustomers) {
      final name = customer.name.toLowerCase();
      final phone = customer.phone?.toLowerCase() ?? '';
      final searchQuery = query.toLowerCase();

      // Full name matches (highest priority)
      if (name.contains(searchQuery)) {
        suggestions.add(customer.name);
        customerNames.add(customer.name);
      }

      // Phone number matches
      if (phone.contains(searchQuery)) {
        suggestions.add(customer.name);
        customerNames.add(customer.name);
      }

      // Individual word matches
      final words = customer.name.split(' ');
      for (final word in words) {
        if (word.toLowerCase().startsWith(searchQuery) && word.length > 2) {
          suggestions.add(word);
        }
      }
    }

    // Add common search patterns (always show some suggestions for testing)
    final commonPatterns = [
      'أحمد علي',
      'محمد حسن',
      'علي أحمد',
      'فاطمة محمد',
      'عائشة علي',
      'خديجة أحمد',
    ];

    for (final pattern in commonPatterns) {
      if (pattern.toLowerCase().contains(query.toLowerCase())) {
        suggestions.add(pattern);
      }
    }

    // If no suggestions found, add some default ones for testing
    if (suggestions.isEmpty && query.length >= 2) {
      suggestions.addAll(
        [
          'أحمد علي محمد',
          'محمد حسن أحمد',
          'علي محمد حسن',
          'فاطمة الزهراء',
        ].where((name) => name.toLowerCase().contains(query.toLowerCase())),
      );
    }

    // Sort suggestions: customer names first, then words
    final sortedSuggestions = suggestions.toList();
    sortedSuggestions.sort((a, b) {
      final aIsCustomer = customerNames.contains(a);
      final bIsCustomer = customerNames.contains(b);

      if (aIsCustomer && !bIsCustomer) return -1;
      if (!aIsCustomer && bIsCustomer) return 1;

      return a.compareTo(b);
    });

    return sortedSuggestions.take(8).toList();
  }

  // دوال البحث المتقدم المنسوخة من "اختر عميل"
  bool _isAdvancedMatch(String text, String query) {
    if (query.isEmpty) return false;

    // تنظيف النصوص من الرموز والمسافات الزائدة
    final cleanText = _cleanArabicText(text);
    final cleanQuery = _cleanArabicText(query);

    // البحث العادي
    if (cleanText.contains(cleanQuery)) return true;

    // البحث بالكلمات المنفصلة
    final words = cleanQuery.split(' ').where((w) => w.isNotEmpty).toList();
    if (words.length > 1) {
      return words.every((word) => cleanText.contains(word));
    }

    // البحث بالأحرف المتتالية (مثل "احمد" يجد "أحمد علي")
    if (cleanQuery.length >= 2) {
      return _isSequentialMatch(cleanText, cleanQuery);
    }

    // البحث بالحرف الأول
    if (cleanQuery.length == 1) {
      return cleanText.startsWith(cleanQuery);
    }

    return false;
  }

  String _cleanArabicText(String text) {
    return text
        .toLowerCase()
        .replaceAll(RegExp(r'[أإآ]'), 'ا') // توحيد الألف
        .replaceAll(RegExp(r'[ىي]'), 'ي') // توحيد الياء
        .replaceAll(RegExp(r'[ةه]'), 'ه') // توحيد التاء المربوطة والهاء
        .replaceAll(RegExp(r'[ؤئ]'), 'و') // توحيد الواو والهمزة
        .replaceAll(
          RegExp(r'[^\u0600-\u06FF\u0750-\u077F\w\s]'),
          '',
        ) // إزالة الرموز
        .replaceAll(RegExp(r'\s+'), ' ') // توحيد المسافات
        .trim();
  }

  bool _isSequentialMatch(String text, String query) {
    if (query.isEmpty) return false;

    int queryIndex = 0;
    bool wordBoundary = true; // للتأكد من بداية الكلمات

    for (int i = 0; i < text.length && queryIndex < query.length; i++) {
      final char = text[i];

      // إذا كان الحرف الحالي يطابق الحرف المطلوب
      if (char == query[queryIndex]) {
        // إذا كنا في بداية كلمة أو الحرف السابق كان مطابقاً
        if (wordBoundary || queryIndex > 0) {
          queryIndex++;
          wordBoundary = false;
        }
      } else if (char == ' ') {
        wordBoundary = true;
      } else {
        wordBoundary = false;
      }
    }

    return queryIndex == query.length;
  }

  bool _isFuzzyMatch(String text, String query) {
    // البحث الضبابي للأخطاء الإملائية البسيطة
    if (query.length < 3) return false;

    final words = text.split(' ');
    for (final word in words) {
      if (_calculateSimilarity(word, query) > 0.7) {
        return true;
      }
    }
    return false;
  }

  double _calculateSimilarity(String s1, String s2) {
    if (s1.isEmpty || s2.isEmpty) return 0.0;

    final longer = s1.length > s2.length ? s1 : s2;
    final shorter = s1.length > s2.length ? s2 : s1;

    if (longer.isEmpty) return 1.0;

    return (longer.length - _levenshteinDistance(longer, shorter)) /
        longer.length;
  }

  int _levenshteinDistance(String s1, String s2) {
    final matrix = List.generate(
      s1.length + 1,
      (i) => List.generate(s2.length + 1, (j) => 0),
    );

    for (int i = 0; i <= s1.length; i++) {
      matrix[i][0] = i;
    }
    for (int j = 0; j <= s2.length; j++) {
      matrix[0][j] = j;
    }

    for (int i = 1; i <= s1.length; i++) {
      for (int j = 1; j <= s2.length; j++) {
        final cost = s1[i - 1] == s2[j - 1] ? 0 : 1;
        matrix[i][j] = [
          matrix[i - 1][j] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j - 1] + cost,
        ].reduce((a, b) => a < b ? a : b);
      }
    }

    return matrix[s1.length][s2.length];
  }

  bool _isPhoneNumberMatch(String phone, String query) {
    // إزالة الرموز والمسافات من رقم الهاتف
    final cleanPhone = phone.replaceAll(RegExp(r'[^\d]'), '');
    final cleanQuery = query.replaceAll(RegExp(r'[^\d]'), '');

    if (cleanQuery.isEmpty) return false;

    return cleanPhone.contains(cleanQuery);
  }

  bool _isNameMatch(String name, String query) {
    // البحث في كلمات منفصلة
    final nameWords = name.split(' ');
    final queryWords = query.split(' ');

    for (final queryWord in queryWords) {
      if (queryWord.trim().isEmpty) continue;

      bool found = false;
      for (final nameWord in nameWords) {
        if (nameWord.toLowerCase().contains(queryWord.toLowerCase())) {
          found = true;
          break;
        }
      }
      if (!found) return false;
    }

    return queryWords.isNotEmpty;
  }
}
